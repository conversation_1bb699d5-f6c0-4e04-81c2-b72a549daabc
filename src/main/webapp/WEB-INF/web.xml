<?xml version="1.0" encoding="UTF-8"?>
<web-app version="2.5" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd">
<!--
Resin下会报错
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	id="polyv" version="2.5">
 -->
	<display-name>Online Video Platform (POLYV)</display-name>

	<!-- 配置 Spring Root 上下文 -->
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath:applicationContext_base.xml
		</param-value>
	</context-param>

	<!-- Spring 上下文监听器 由此启动Spring上下文容器 -->
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>

	<filter>
		<filter-name>requestTraceFilter</filter-name>
		<filter-class>net.polyv.web.filter.RequestTraceFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>requestTraceFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>encodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>encodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!-- 配置SpringMVC Servlet -->

	<servlet>
		<servlet-name>dispatcher</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<!-- 配置SpringMVC 上下文 -->
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath:dispatcher-servlet.xml</param-value>
		</init-param>
		<init-param>
      		<param-name>dispatchOptionsRequest</param-name>
      		<param-value>true</param-value>
    	</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>dispatcher</servlet-name>
		<url-pattern>/uc/*</url-pattern>
	</servlet-mapping>

	<!-- 配置HiddenHttpMethodFilter覆盖Http请求方法 默认以_method为参数, 例如 /xxx?_method=PUT
		Servlet容器将会识别为PUT方法的请求 -->
	<filter>
		<filter-name>hiddenHttpMethodFilter</filter-name>
		<filter-class>org.springframework.web.filter.HiddenHttpMethodFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>hiddenHttpMethodFilter</filter-name>
		<servlet-name>dispatcher</servlet-name>
	</filter-mapping>
	<jsp-config>
		<jsp-property-group>
			<url-pattern>*.jsp</url-pattern>
			<el-ignored>false</el-ignored>
		</jsp-property-group>
	</jsp-config>
	<session-config>
		<session-timeout>30</session-timeout>
	</session-config>
	<error-page>
		<error-code>403</error-code>
		<location>/WEB-INF/views/error/403.vm</location>
	</error-page>
	<error-page>
		<error-code>404</error-code>
		<location>/WEB-INF/views/error/404.vm</location>
	</error-page>
    <!--
	<error-page>
		<error-code>500</error-code>
        <location>/WEB-INF/views/error/500.jsp</location>
	</error-page>  -->

	<!-- JDBC DataSource -->
	<resource-ref>
		<description>DB Connection</description>
		<res-ref-name>jdbc/polyv</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
	</resource-ref>

    <!-- 验证码 -->
    <servlet>
        <servlet-name>Kaptcha</servlet-name>
        <servlet-class>com.google.code.kaptcha.servlet.KaptchaServlet</servlet-class>
        <init-param>
            <param-name>kaptcha.border</param-name>
            <param-value>no</param-value>
        </init-param>
        <init-param>
            <param-name>kaptcha.textproducer.font.color</param-name>
            <param-value>black</param-value>
        </init-param>

        <init-param>
            <param-name>kaptcha.textproducer.char.space</param-name>
            <param-value>5</param-value>
        </init-param>
    </servlet>

    <servlet-mapping>
        <servlet-name>Kaptcha</servlet-name>
        <url-pattern>/kaptcha.jpg</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Kaptcha</servlet-name>
        <url-pattern>/uc/kaptcha.jpg</url-pattern>
    </servlet-mapping>

</web-app>
