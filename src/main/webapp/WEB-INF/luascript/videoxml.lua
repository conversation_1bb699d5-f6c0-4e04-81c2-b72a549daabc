local redis = require 'redis'
local client,err = redis.connect('r-wz98k4q4ss352h5vla.redis.rds.aliyuncs.com', 6379)
if not client then
    ngx.say("failed to connect redis:", err)
    return
end



local m, err = ngx.re.match(ngx.var.uri, ".*/(?<vid>[a-z0-9_]+)", "i")
if m then
    local vid = m['vid']
    local userid = string.sub(vid,0,10)

    local args = ngx.req.get_uri_args()
    local callback = args["callback"]
    local replies, err = client:mget('videoxml_'..vid , "userxml_"..userid)
    if not replies or replies[1]==nil or replies[2]==nil then
        ngx.exec("@backend")
    else
        local videoxml = replies[1]
        local userxml = replies[2]
        local allxml = "{\"uxml\":\""..userxml.."\",\"vxml\":\""..videoxml.."\"}"
        ngx.say(allxml)
        return
    end

else
    ngx.say("vid not found.")
end