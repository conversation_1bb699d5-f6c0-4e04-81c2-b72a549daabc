local redis = require 'redis'
local client,err = redis.connect('r-wz98k4q4ss352h5vla.redis.rds.aliyuncs.com', 6379)
if not client then
    ngx.say("failed to connect redis:", err)
    return
end



local m, err = ngx.re.match(ngx.var.uri, ".*/(?<vid>[a-z0-9_]+)\\.js", "i")
if m then
    local vid = m['vid']
    local userid = string.sub(vid,0,10)

    local args = ngx.req.get_uri_args()
    local callback = args["callback"]
    local replies, err = client:mget('videojson_'..vid , "userjson_"..userid)
    if not replies or not replies[1] or not replies[2] then
        ngx.exec("@backend")
    else
        local videojson = string.sub(replies[1],2,string.len(replies[1])-1)
        local userjson = string.sub(replies[2],2,string.len(replies[2])-1)
        local alljson = "{"..videojson..","..userjson.."}"
        --local alljson = "{"..videojson..","..userjson..", hash:''}"
        if not callback then
            ngx.say(alljson)
            return
        else
           ngx.say(callback..'('..alljson..');')
        end
    end

else
    ngx.say("vid not found.")
end