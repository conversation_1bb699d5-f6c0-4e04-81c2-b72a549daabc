<ul class="tree treeFolder expand">
	<li><a icon="/file/logo/new.png" href="https://my.polyv.net/" fresh="false" target="_blank" rel="newversion" mask="true" height="350" width="530" id="newversion">体验新版</a></li>
	<li><a icon="/file/logo/x1.png" href="/uc/video/upload" fresh="false" target="navTab" rel="video_upload" mask="true" height="350" width="530" id="uploadbutton">#springMessage("menu_template.upload")</a></li>
	<li><a icon="/file/logo/x2.png" href="/uc/video/vnew" target="navTab" rel="video_list" mask="true" height="350" width="530">#springMessage("menu_template.new")</a></li>
	#if($packageid!=35)
	##<li><a icon="/file/logo/x4.png" href="/uc/video/playersetting" target="navTab" rel="playersetting">播放设置</a></li>
	  	#if("$!{userlevel}"=="" || $!{userlevel}==2)
	 #if($has8=="true") 	
	<li><a icon="/file/logo/x51.png" href="/uc/video/getHosturl" target="navTab" rel="s1">#springMessage("menu_template.hostUrl")</a></li>
	#end
	<li><a icon="/file/logo/x4.png" href="/uc/video/getMoerPlayerurl" target="navTab" rel="playersetting">#springMessage("menu_template.playersetting")</a></li>
	<li><a icon="/file/logo/x22.png" href="/uc/video/encryption" target="navTab" rel="s2">#springMessage("menu_template.encryption")</a></li>
	 #if($has10=="true") 
    <li><a icon="/file/logo/x52.png" href="/uc/video/settings/sourcedistributionget" target="navTab" rel="s0" id="s0" class="basea">#springMessage("menu_template.sourcedistribution")</a></li>
	 #end   
	    #end
	#end
	 #if($has17=="true") 
     <li><a icon="/file/logo/playlist-icon.png" href="/uc/video/news?type=2" target="navTab" rel="s9" id="s9" class="playerlist">#springMessage("menu_template.playerlist")</a></li>
	#end
	 #foreach($p in $!permissions)#if($!p.id==42)
	 <li><a icon="/file/logo/x5.png" href="/uc/questionnaire/userinforecord" target="navTab" rel="s10" id="s10" class="userinforecord">#springMessage("menu_template.userinforecord")</a></li>
	 #end
	 #end
	##$!{encryptedconnection}
	
	
	#foreach($p in $permissions)#if($!p.id==13)
		<li class="video_cata">
			<a href="/uc/video/vnew" target="navTab" rel="cata_${mediatype}">#springMessage("menu_template.cata")</a>
			<ul>
			</ul>
        </li>
	#end#end
	<li><a icon="/file/logo/rsync.png" href="${graburl}" target="navTab" rel="recycle_${mediatype}" title="#springMessage("menu_template.fileSync")">#springMessage("menu_template.fileSync")</a></li>
    #if("$!{userlevel}"=="")
	<li><a icon="/file/logo/x6.png" href="/uc/video/recycle" target="navTab" rel="recycle_${mediatype}" title="#springMessage("menu_template.Trash")">#springMessage("menu_template.Trash")</a></li>
	#end
</ul>