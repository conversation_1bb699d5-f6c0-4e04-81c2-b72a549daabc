<script>
	$(function(){
	$("#pwsend").click(function(){
	var newPassword1 = $("#newPassword1").val();
	var newPassword2 = $("#newPassword2").val();
    var oldPassword = $("#oldPassword").val();

    if(newPassword1==""){
       alertMsg.warn('#springMessage("edit-password.passconfirm")！');
       return false;
      }
        if(newPassword2==""){
       alertMsg.warn('#springMessage("edit-password.passconfirmagain")！');
       return false;
      }
      
      if(newPassword1.length<6){
          alertMsg.warn('#springMessage("edit-password.passconfirmlength")！');
          return false;
      }
      if(newPassword1 !=newPassword2){
         alertMsg.warn("#springMessage("edit-password.passconfirmmatch")！");
         return false;
      }
	$.post("/uc/user/editpassword",
	{newPassword:$("#newPassword1").val(),oldPassword:$("#oldPassword").val()
	},
	function(data,textStatus){
		
		if("fail"==textStatus){
		alertMsg.warn('修改失败！');
		return false;
	}
		if("success"==textStatus){
		alertMsg.correct('修改成功！');
		
	}
	
	})
	
	})

	})
	</script>
<h2 class="contentTitle">#springMessage("edit-password.modify")</h2>

<form method="post" action="ajaxDone.html" class="pageForm required-validate" >
	<div class="pageContent" style="padding:5px;">
		
		<div class="panel">
			<h1>#springMessage("edit-password.modify")</h1>
			<div>
				<p class="mt5">
					<label style="width:90px">#springMessage("edit-password.oldpassword")：</label>
					<input type="password" name="oldPassword" id="oldPassword" size="30" class="required" alt="不能为空"/>
				</p>
				<p class="mt5">
					<label style="width:90px">#springMessage("edit-password.newpassword")：</label>
					<input type="password" name="newPassword1" id="newPassword1" size="30" class="required" alt="不能为空"/>
				</p>
				<p class="mt5">
					<label style="width:90px">#springMessage("edit-password.newpassword2")：</label>
					<input type="password" name="newPassword2" id="newPassword2" size="30" class="required " alt="不能为空"/>
				</p>
			</div>
		</div>
		
	</div>

	<div class="formBar bottomx button_center">
		<input type="button" value="#springMessage("userInfoRecord.save")" id="pwsend" />
	</div>

</form>
