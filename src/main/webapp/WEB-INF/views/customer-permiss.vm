<form id="pagerForm" method="post" action="/uc/user/list?permiss=1">
	<input type="hidden" name="status" value="${param.status}">
	<input type="hidden" name="keywords" value="${keywords}" />
	<input type="hidden" name="pageNum" value="1" />
	<input type="hidden" name="numPerPage" value="${numPerPage}" />
	<input type="hidden" name="orderField" value="${param.orderField}" />
</form>


<div class="pageHeader">
	<form onsubmit="return navTabSearch(this);" action="/uc/user/list" method="post">
<div class="pageContent">
	<div class="panelBar">
		<ul class="toolBar">
			<li><a class="add" href="/uc/user/edit" target="dialog" max="true" title="增加用户" width="400" height="600"><span>添加</span></a></li>
			<li><a class="delete" href="/uc/user/delete?autoid={autoid}" target="navTabTodo" title="确定要删除吗?"><span>删除</span></a></li>
			<li><a class="edit" href="/uc/user/edit?autoid={autoid}&permiss=1" target="navTab"><span>修改</span></a></li>
			<li class="line">line</li>
		</ul>
	</div>
	<table class="table" width="100%" layoutH="138">
		<thead>
				<th width="50"></th>
				<th width="100">邮箱地址</th>
				<th width="80">用户域名</th>
				<th width="80">用户名称</th>
				<th width="80">公司名称</th>
				<th width="80">用户角色</th>
				<th width="100">用户权限</th>
				<th width="80">状态</th>
			</tr>
		</thead>
		<tbody>
		#foreach($customer in $customerList)
        <tr target="autoid" rel="${customer.autoid}">
				<td><input type="checkbox" name="mid" id="mid" value ="${customer.autoid}"></td>
				<td><a href="/uc/user/get?autoid=${customer.autoid}&permiss=1" target="navTab" title="用户权限" rel="autoid">${customer.email}</a></td>
				<td>${customer.weburl}</td>
				<td>${customer.name}</td>
				<td>${customer.company}</td>
				<td>${customer.grade}</td>
				<td>${customer.grade}</td>
				<td>${customer.status}</td>
			</tr>
        #end
			
		</tbody>
	</table>
	<div class="panelBar">
		<div class="pages">
			<span>显示</span>
			<select class="combox" name="numPerPage" change="navTabPageBreak" param="numPerPage">
				<option value="10">10</option>
				<option value="15">15</option>
				<option value="20">20</option>
				<option value="25">25</option>
			</select>
			<span>条，共${totalCount}条</span>
		</div>
		
		<div class="pagination" targetType="navTab" totalCount="${totalCount}" numPerPage="${numPerPage}" pageNumShown="10" currentPage="${currentPage}"></div>

	</div>
</div>
