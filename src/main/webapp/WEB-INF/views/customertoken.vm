<div>

<script>
	function posttoken(url){
		 if(confirm("#springMessage("customertoken.warn")")){
		$("#posttoken").attr("action",url).submit();
		$(".tree a[rel='token']").click();
		}
	}
	
	function setSign(radio){
		$.post( "/uc/user/setSign", { sign: radio.value }).done(function(data) {
		  alert(data);
		}); 
		
	}
</script>
<style>
.line{
display:block;
width:500px;
height:30px;
line-height:30px;
}
.btn {
 display:block;
 width:20px;
 height:6px;
 background:#fff url(/img/btn_bg2.gif) 0 0;
 border:1px solid #90be4a;
 font-size:12px; 
 font-weight:bold; 
 line-height:20px; 
 margin-left:20px;
 cursor:pointer;
 text-align:center;
 float:left;
 }
</style>
<h2 class="contentTitle">#springMessage("menu.api")</h2>
<form method="post" id="posttoken" action="" class="pageForm required-validate" onsubmit="return validateCallback(this)" style="padding:5px">
	<div class="panel">
		<h1>#springMessage("customertoken.code")</h1>
		<div class="pageFormContent">
			<h3>#springMessage("customertoken.tip")</h3>
			#foreach($p in $!permissions)#if($!p.id==14)
			<div class="mt5">
				<label>userId:</label>
				<input id="userid" name="" type="text" size="35" value="$!{customer.getUserid()}" readonly="readonly" />
				
			</div>
			<div class="mt5">
				<label>writetoken:</label>
				<input id="wtoken" name="" type="text" size="35" value="$!{writetoken}" readonly="readonly" />
   
				<a class="button" href="javascript:tokencopy('#wtoken',$('#wtoken').val())" style="margin-left:20px;"><span>#springMessage("customertoken.copy")</span></a>
				<a class="button" href="javascript:posttoken('/uc/user/setwtoken')" style="margin-left:20px;"><span>#springMessage("customertoken.change")</span ></a>
			</div>
			<div class="mt5">
				<label>readtoken:</label>
				<input id="rtoken" name=""  type="text" size="35" value="$!{readtoken}" readonly="readonly" />
				<a class="button" href="javascript:tokencopy('#rtoken',$('#rtoken').val())" style="margin-left:20px;"><span>#springMessage("customertoken.copy")</span></a>
				<a class="button" href="javascript:posttoken('/uc/user/setrtoken')" style="margin-left:20px;"><span>#springMessage("customertoken.change")</span></a>
			</div>
			
				<div class="mt5">
				<label>secretkey:</label>
				<input id="stoken" name=""  type="text" size="35" value="$!{secretkey}" readonly="readonly" />
				<a class="button" href="javascript:tokencopy('#stoken',$('#stoken').val())" style="margin-left:20px;"><span>#springMessage("customertoken.copy")</span></a>
				<a class="button" href="javascript:posttoken('/uc/user/setsecretkey')" style="margin-left:20px;"><span>#springMessage("customertoken.change")</span></a>
			</div>
			#end#end

			<div class="mt5">
			<label style="width:180px;">#springMessage("customertoken.sign")：</label>
				<label for="radio" style="width:12px">#springMessage("customertoken.yes")</label>
				<input type="radio" name="sign" id="radio" value="1" onclick="setSign(this)" #if($!customer.sign==true) checked="checked" #end />
				<label for="radio2" style="width:12px">#springMessage("customertoken.no")</label>
				<input type="radio" name="sign" id="radio" value="0" onclick="setSign(this)" #if($!customer.sign==false) checked="checked" #end/>
			</div>
			
			<div class="mt5">
				<label><a href="http://dev.polyv.net/">Developer center</a></label>
			</div>
		</div>
	</div>
#foreach($p in $!permissions)#if($!p.id==14)
	<div class="panel mt5">
		<h1>#springMessage("customertoken.uploader")</h1>
		<div>
		上传插件可以把Polyv上传和部分视频管理功能集成到你的网站后台，<br>
		<a href="http://dev.polyv.net/2013/07/polyvuploadplugin/" target="_blank" style="text-decoration: underline">点击查看如何集成插件到您的网站后台</a>
		
		</div>


		
	</div>
	
	<div class="panel mt5">
		<h1>#springMessage("customertoken.appcode")</h1>
		<div>
			<label style="width:180px;">#springMessage("customertoken.appcode"):</label>
			<textarea id="appcode" name="appcode" rows="3" cols="50" readonly="readonly" style="margin: 0px; height: 65px; width: 328px;" >${appBASECode}</textarea>
			<a class="button" href="javascript:tokencopy('#appcode',$('#appcode').val())" style="margin-left:20px;"><span>#springMessage("customertoken.copy")</span></a>
		</div>
		
	</div>
	#end#end
<script>
 function tokencopy(o,code) {
			
			if(window.clipboardData){
				window.clipboardData.clearData();window.clipboardData.setData("Text",code);
			}else {
				alert("请在关闭对话框后按CTRL+C"); 
				$(o).focus();
				$(o).select();
			}
		}

</script>	
	
</form>