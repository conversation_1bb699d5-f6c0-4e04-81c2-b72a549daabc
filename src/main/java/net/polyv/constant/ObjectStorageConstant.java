package net.polyv.constant;

import com.aliyun.oss.internal.OSSHeaders;

/**
 * <AUTHOR>
 * @type ObjectStorageConstant
 * @description
 */
public class ObjectStorageConstant {

    private ObjectStorageConstant() {}

    public static final String DOT_MARK = ".";

    // 中划线
    public static final String MIDDLINE = "-";

    // 逗号
    public static final String COMMA = ",";

    // 百分号
    public static final String PERCENTS_DOT = "%s.";

    public static final String CONFIG_NAMESPACE = "backend.object-storage";

    public static final String CONFIG_PREFIX = "object.storage.";

    public static final String APP_ID_KEY = "app-id";

    public static final String SECRET_ID_KEY = "secret-id";

    public static final String ACCESS_KEY_KEY = "access-key";

    public static final String SECRET_KEY_KEY = "secret-key.private-key";

    public static final String NAME_KEY = "name";
    public static final String DOMAIN_KEY = "domain";
    public static final String ENDPOINT_KEY = "endpoint";

    public static final String REGION_KEY = "region";
    
    public static final String ALI_X_OSS_RESTORE = OSSHeaders.OSS_RESTORE;
    
    public static final String HW_X_OBS_RESTORE = "x-obs-restore";
    
    /* 处理中的请求 */
    public static final String ON_GONING_REQUEST_FALSE = "ongoing-request=\"false\"";
    
    public static final String ON_GONING_REQUEST_TRUE = "ongoing-request=\"true\"";
}
