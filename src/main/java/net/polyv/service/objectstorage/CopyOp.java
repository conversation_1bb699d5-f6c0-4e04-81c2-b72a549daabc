package net.polyv.service.objectstorage;

import net.polyv.commons.type.storage.Bucket;

/**
 * <AUTHOR>
 * @type CopyOp
 * @description 拷贝
 */
public interface CopyOp {

    /**
     * @description 拷贝 (存在则覆盖)
     * @param sourceKey 源存储对象路径
     * @param destinationBucket 目的桶名称
     * @param destinationKey 目的存储对象路径
     * @return true-失败，false-成功
     * <AUTHOR>
     */
    boolean copy(String sourceKey, Bucket destinationBucket, String destinationKey);


    /**
     * @description 拷贝
     * @param sourceKey 源存储对象路径
     * @param destinationBucket 目的桶名称
     * @param destinationKey 目的存储对象路径
     * @param override 是否覆盖 | true-是，false-否
     * @return true-失败，false-成功
     * <AUTHOR>
     */
    boolean copy(String sourceKey, Bucket destinationBucket, String destinationKey, boolean override);
}
