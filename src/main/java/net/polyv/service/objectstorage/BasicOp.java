package net.polyv.service.objectstorage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @type BasicOp
 * @description 基础操作
 */
public abstract class BasicOp {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * @description
     * @param localKeys 本地路径
     * @param remoteKeys 远程路径
     * <AUTHOR>
     */
    protected void check(final List<String> localKeys, final List<String> remoteKeys) {
        if (null == localKeys || localKeys.isEmpty() || null == remoteKeys || remoteKeys.isEmpty() ||
                localKeys.size() != remoteKeys.size()) {
            final String message = String.format("invalid paths, localPaths=%s, httpPaths=%s", localKeys, remoteKeys);
            logger.info(message);
            throw new IllegalArgumentException(message);
        }
    }

}
