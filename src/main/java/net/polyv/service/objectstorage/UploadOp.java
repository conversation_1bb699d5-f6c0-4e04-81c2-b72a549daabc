package net.polyv.service.objectstorage;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @type UploadOp
 * @description 上传
 */
public interface UploadOp {

    /**
     * @description 上传存储对象
     * @param localKey 本地路径，例如 mp4/abc/01.mp4
     * @param remoteKey 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * <AUTHOR>
     */
    Optional<String> upload(final String localKey, final String remoteKey);
    
    /**
     * @description 上传存储对象
     * @param localKey 本地路径，例如 mp4/abc/01.mp4
     * @param remoteKey 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @param headers 请求头
     * @return true-成功，false-失败
     * <AUTHOR>
     */
    default Optional<String> upload(final String localKey, final String remoteKey, final Map<String, String> headers) {
        return Optional.empty();
    }

    /**
     * @description 上传存储对象
     * @param inputStream 输入流
     * @param remoteKey 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * <AUTHOR>
     */
    Optional<String> upload(final InputStream inputStream, final String remoteKey);

    /**
     * @description 上传多个存储对象
     * @param localKeys 本地路径集合，例如 mp4/abc/01.mp4
     * @param remoteKeys 存储对象路径集合 | http 路径，例如 mp4/abc/01.mp4
     * @throws IllegalArgumentException 如果 localKeys 或 remoteKeys 为空，或者两个列表长度不一致，则抛出该异常
     * @return true-成功，false-失败
     * <AUTHOR>
     */
    Optional<List<String>> uploads(final List<String> localKeys, final List<String> remoteKeys);

    /**
     * @description 上传归档存储对象
     * @param localKey 本地路径，例如 mp4/abc/01.mp4
     * @param remoteKey 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * <AUTHOR>
     */
    Optional<String> uploadArchive(final String localKey, final String remoteKey);

    /**
     * @description 上传归档存储对象
     * @param inputStream 输入流
     * @param remoteKey 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * <AUTHOR>
     */
    Optional<String> uploadArchive(final InputStream inputStream, final String remoteKey);

    /**
     * @description 上传归档存储对象
     * @param localKeys 本地路径集合，例如 mp4/abc/01.mp4
     * @param remoteKeys 存储对象路径集合 | http 路径，例如 mp4/abc/01.mp4
     @throws IllegalArgumentException 如果 localKeys 或 remoteKeys 为空，或者两个列表长度不一致，则抛出该异常
     * @return true-成功，false-失败
     * <AUTHOR>
     */
    Optional<List<String>> uploadArchives(final List<String> localKeys, final List<String> remoteKeys);

}
