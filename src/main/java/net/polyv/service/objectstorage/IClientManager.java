package net.polyv.service.objectstorage;

import net.polyv.commons.type.storage.Bucket;

/**
 * <AUTHOR>
 * @type IClientManager
 * @description
 */
public interface IClientManager {

    // 读取配置时的默认值
    String DEFAULT_CONFIG = null;

    /**
     * @description 重建对象
     * <AUTHOR>
     */
    void rebuild();

    /**
     * @description 销毁(进程退出用)
     * <AUTHOR>
     */
    void destroy();

    /**
     * @description 获取桶的配置KEY(枚举的字面字符的小写)
     * @param bucket 桶
     * @return 配置KEY
     * <AUTHOR>
     */
    default String getConfigKey(final Bucket bucket) {
        return bucket.getConfigKey();
    }

    /**
     * @description (从配置)获取桶名称
     * @param bucket 桶
     * @return 桶名称
     * <AUTHOR>
     */
    String getName(final Bucket bucket);

}
