package net.polyv.service.objectstorage;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @type AccessOp
 * @description 访问
 */
public interface AccessOp {

    /**
     * @description 判断存储对象是否存在
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return  true-成功，false-失败
     * <AUTHOR>
     */
    boolean exists(String key);

    /**
     * @description 获取一个存储对象的访问 URL
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return 例如 http://vod-assets.videocc.net/test/test1.txt
     * <AUTHOR>
     */
    Optional<String> getUrl(final String key);

    /**
     * @description 获取一个存储对象的临时访问 URL
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @param duration 有效期持续时间
     * @return 例如 http://vod-assets.videocc.net/test/test1.txt
     * <AUTHOR>
     */
    Optional<String> getUrl(final String key, final Duration duration);

    /**
     * @description 恢复归档
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return 成功-true，失败-false
     * <AUTHOR>
     */
    boolean restore(final String key);

    /**
     * @description 恢复归档
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @param expirationInDays 多少天过期
     * @return true-成功，false-失败
     * <AUTHOR>
     */
    boolean restore(final String key, final int expirationInDays);

    /**
     * @description 是否归档状态
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-归档，false-非归档
     * <AUTHOR>
     */
    boolean isArchived(final String key);

    /**
     * @description 列出指定前缀的KEY列表
     * @param prefix KEY前缀，例如 abc/123
     * @return KEY列表
     * <AUTHOR>
     */
    List<String> listByPrefix(final String prefix);
    
    /**
     * @description 是否可访问
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-已经恢复，false-未恢复
     * <AUTHOR>
     */
    boolean accessable(final String key);


}
