package net.polyv.service.objectstorage;

import net.polyv.commons.type.storage.Bucket;

/**
 * <AUTHOR>
 * @type IObjectService
 * @description
 */
public interface IObjectService {

    /**
     * @description 访问操作
     * @param bucket 桶
     * @return 操作对象
     * <AUTHOR>
     */
    AccessOp boundForAccess(final Bucket bucket);

    /**
     * @description 下载操作
     * @param bucket 桶
     * @return 操作对象
     * <AUTHOR>
     */
    DownloadOp boundForDownload(final Bucket bucket);

    /**
     * @description 上传操作
     * @param bucket 桶
     * @return 操作对象
     * <AUTHOR>
     */
    UploadOp boundForUpload(final Bucket bucket);

    /**
     * @description 删除操作
     * @param bucket 桶
     * @return 操作对象
     * <AUTHOR>
     */
    DeleteOp boundForDelete(final Bucket bucket);

    /**
     * @description 拷贝操作 (同地域复制)
     * @param srcBucket 源桶
     * @return 操作对象
     * <AUTHOR>
     */
    CopyOp boundForCopy(final Bucket srcBucket);

    /**
     * @description 拷贝
     * @param sourceBucket 来源桶
     * @param sourceKey 源存储对象路径
     * @param destinationBucket 目的桶名称
     * @param override 是否覆盖 | true-是，false-否
     * @return true-失败，false-成功
     * <AUTHOR>
     */
    default boolean copy(Bucket sourceBucket ,
                         String sourceKey,
                         Bucket destinationBucket,
                         String destinationKey ,
                         boolean override){
        return false ;
    }

    /**
     * @description 销毁
     * <AUTHOR>
     */
    default void destroy() {}

    /**
     * 获取桶配置信息
     * @param bucket
     * @return
     */
    default BucketConfigProperties getBucketConfigProperties(Bucket bucket){
        return null ;
    }
    
    /**
     * @description 更新操
     * @param bucket 源桶
     * @return 操作对象
     * <AUTHOR>
     */
    default UpdateOp boundForUpdate(final Bucket bucket) {
        throw new RuntimeException("Unsupported operation");
    }
    
}
