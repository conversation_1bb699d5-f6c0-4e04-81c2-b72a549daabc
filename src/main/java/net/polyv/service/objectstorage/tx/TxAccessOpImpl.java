package net.polyv.service.objectstorage.tx;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.CASJobParameters;
import com.qcloud.cos.model.COSObject;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.RestoreObjectRequest;
import com.qcloud.cos.model.StorageClass;
import com.qcloud.cos.model.Tier;
import lombok.AllArgsConstructor;
import net.polyv.service.objectstorage.AccessOp;

import java.net.URL;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @type AliAccessOpImpl
 * @description
 */
@AllArgsConstructor
public class TxAccessOpImpl extends COSBasicOp implements AccessOp {

    private COSClient client;
    
    private String domain;

    private String bucket;

    /* 恢复回档失效天数 */
    private int expirationInDays;

    /**
     * @description 判断存储对象是否存在
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return  true-成功，false-失败
     * <AUTHOR>
     */
    @Override
    public boolean exists(String key) {
        return client.doesObjectExist(bucket, key);
    }

    /**
     * @param key    存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return 例如 <a href="http://vod-assets.videocc.net/test/test1.txt">...</a>
     * @description 获取一个存储对象的访问 URL
     * <AUTHOR>
     */
    @Override
    public Optional<String> getUrl(String key) {
        return Optional.ofNullable(super.getUrl(domain, bucket, key));
    }
    /**
     * @param key      存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @param duration 有效期持续时间
     * @return 例如 <a href="http://vod-assets.videocc.net/test/test1.txt">...</a>
     * @description 获取一个存储对象的临时访问 URL
     * <AUTHOR>
     */
    @Override
    public Optional<String> getUrl(final String key, final Duration duration) {
        final GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucket, key);
        // 失效时间
        final Date expiration = new Date(System.currentTimeMillis() + duration.toMillis());
        request.setExpiration(expiration);
        request.setMethod(HttpMethodName.GET);
        try {
            // 自定义请求头和参数
            final URL url = client.generatePresignedUrl(request);

            return Optional.ofNullable(url.toString());
        } catch (Exception e) {
            logger.error("getUrl key: {} exception: ", key, e);
        }

        return Optional.empty();
    }

    /**
     * @param key    存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return 成功-true，失败-false
     * @description 恢复归档 (默认失效日期一天-可配置)
     * <AUTHOR>
     */
    @Override
    public boolean restore(final String key) {
        return restore(key, expirationInDays);
    }

    /**
     * @param key              存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @param expirationInDays 多少天过期
     * @return true-成功，false-失败
     * @description 恢复归档
     * <AUTHOR>
     */
    @Override
    public boolean restore(final String key, final int expirationInDays) {
        final RestoreObjectRequest request = new RestoreObjectRequest(bucket, key);
        // // 设置 restore 得到的临时副本过期天数为1天
        // 必须恢复归档对象，必须设置失效日期 | The expiration in days parameter must be specified when copying a cas object
        request.setExpirationInDays(expirationInDays);
        // 设置恢复模式为 Standard，其他的可选模式包括 Expedited 和 Bulk。若恢复归档存储类型数据，则支持上述三种恢复模式，
        // 选择不同恢复模式，在费用和恢复速度上不一样。若恢复深度归档存储类型数据，则仅支持 Standard 和 Bulk 恢复模式
        final CASJobParameters casJobParameters = new CASJobParameters();
        // Expedited-加快 | 急速(Expedited)模式: 1~5min, 标准(Standard)模式: 3~5h, 批量(Bulk)模式: 5~12h
        casJobParameters.setTier(Tier.Standard);
        try {
            client.restoreObject(request);
        } catch (Exception e) {
            logger.error("restore bucket: {}, key: {}, exception: ", bucket, key, e);
            return false;
        }

        return true;
    }

    /**
     * @param key    存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-归档，false-非归档
     * @description 是否归档状态
     * <AUTHOR>
     */
    @Override
    public boolean isArchived(final String key) {
        final COSObject cosObject = client.getObject(bucket, key);
        if (null == cosObject) {
            return false;
        }
        final ObjectMetadata metadata = cosObject.getObjectMetadata();

        return isArchived(metadata.getStorageClassEnum());
    }

    /**
     * @param prefix KEY前缀，例如 abc/123
     * @return KEY列表
     * @description 列出指定前缀的KEY列表
     * <AUTHOR>
     */
    @Override
    public List<String> listByPrefix(final String prefix) {
      return super.listByPrefix(client, bucket, prefix);
    }

    /**
     * @description 是否为归档类型
     * @param storageClass 存储类型
     * @return 是否归档
     * <AUTHOR>
     */
    private boolean isArchived(final StorageClass storageClass) {
        return StorageClass.Archive == storageClass
                || StorageClass.Deep_Archive == storageClass
                || StorageClass.Maz_Archive == storageClass
                || StorageClass.Maz_Deep_Archive == storageClass;
    }
    
    /**
     * @param key 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-已经恢复，false-未恢复
     * @description 是否可访问
     * <AUTHOR>
     */
    @Override
    public boolean accessable(String key) {
        throw new UnsupportedOperationException();
    }
}
