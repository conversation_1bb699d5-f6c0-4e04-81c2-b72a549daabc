package net.polyv.service.objectstorage.tx;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.region.Region;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @type COSClientFactory
 * @description COS客户端工厂
 */
@Getter
class COSClientFactory {

    /*  */
    private final String secretId;

    private final String secretKey;

    private final COSConfigProperties.ClientProperties clientProperties;

    private final COSClient client;

    public COSClientFactory(final COSConfigProperties.ClientProperties clientProperties) {
        this.secretId = clientProperties.getSecretId();
        this.secretKey = clientProperties.getSecretKey().getPrivateKey();
        this.clientProperties = clientProperties;
        // 创建客户端
        client = newClient();
    }

    /**
     * @description 初始化
     * <AUTHOR>
     */
    private synchronized COSClient newClient() {
        // 重新读取配置，构建客户端对象
        final COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 2 设置 bucket 的地域, COS 地域的简称请参见 https://cloud.tencent.com/document/product/436/6224
        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
        // 区域名称参考: https://cloud.tencent.com/document/product/436/6224
        final Region region = new Region(clientProperties.getRegion());
        final ClientConfig clientConfig = new ClientConfig(region);
        // 这里建议设置使用 https 协议
        // 从 5.6.54 版本开始，默认使用了 https
        if (null != clientProperties.getHttpProtocol()) {
            clientConfig.setHttpProtocol(clientProperties.getHttpProtocol());
        }
        if (null != clientProperties.getSignExpired()) {
            clientConfig.setSignExpired(clientProperties.getSignExpired());
        }
        if (null != clientProperties.getConnectionRequestTimeout()) {
            clientConfig.setConnectionRequestTimeout(clientProperties.getConnectionRequestTimeout());
        }
        if (null != clientProperties.getConnectionTimeout()) {
            clientConfig.setConnectionTimeout(clientProperties.getConnectionTimeout());
        }
        if (null != clientProperties.getSocketTimeout()) {
            clientConfig.setSocketTimeout(clientProperties.getSocketTimeout());
        }
        if (null != clientProperties.getMaxConnectionsCount()) {
            clientConfig.setMaxConnectionsCount(clientProperties.getMaxConnectionsCount());
        }
        if (null != clientProperties.getIdleConnectionAlive()) {
            clientConfig.setIdleConnectionAlive(clientProperties.getIdleConnectionAlive());
        }
        if (StringUtils.isNotEmpty(clientProperties.getUserAgent())) {
            clientConfig.setUserAgent(clientProperties.getUserAgent());
        }
        if (null != clientProperties.getReadLimit()) {
            clientConfig.setReadLimit(clientProperties.getReadLimit());
        }

        // 3 生成 cos 客户端。
       return new COSClient(cred, clientConfig);
    }

    /**
     * @description 获取客户端
     * @return 访问客户端
     * <AUTHOR>
     */
    public COSClient getClient() {
        return client;
    }

    /**
     * @description 销毁客户端
     * <AUTHOR>
     */
    public synchronized void destroy() {
        client.shutdown();
    }

}
