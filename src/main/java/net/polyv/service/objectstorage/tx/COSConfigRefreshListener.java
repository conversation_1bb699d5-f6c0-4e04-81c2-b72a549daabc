package net.polyv.service.objectstorage.tx;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description COS配置刷新监听器
 * 当COS配置发生变更时，自动刷新相关的客户端连接
 */
@Component
@Slf4j
public class COSConfigRefreshListener implements ApplicationListener<COSConfigRefreshEvent> {

    @Resource
    private COSClientManager cosClientManager;

    @Override
    public void onApplicationEvent(COSConfigRefreshEvent event) {
        log.info("Received COS configuration refresh event, changed keys: {}", event.getChangedKeys());
        
        try {
            // 刷新客户端管理器
            if (cosClientManager != null) {
                // 这里可以调用客户端管理器的刷新方法
                // 例如：清理现有连接，重新创建客户端等
                refreshClientManager(event);
            }
            
            log.info("COS configuration refresh handled successfully");
        } catch (Exception e) {
            log.error("Failed to handle COS configuration refresh", e);
        }
    }

    /**
     * 刷新客户端管理器
     * 
     * @param event 配置刷新事件
     */
    private void refreshClientManager(COSConfigRefreshEvent event) {
        // 这里可以实现具体的客户端刷新逻辑
        // 例如：
        // 1. 清理现有的客户端连接
        // 2. 重新初始化客户端工厂
        // 3. 更新连接池配置等
        
        log.info("Refreshing COS client manager due to configuration changes");
        
        // 如果 COSClientManager 有刷新方法，可以在这里调用
        // cosClientManager.refresh();
        
        // 或者可以调用销毁方法，让客户端在下次使用时重新创建
        try {
            cosClientManager.destroy();
            log.info("COS client manager destroyed, clients will be recreated on next use");
        } catch (Exception e) {
            log.warn("Failed to destroy COS client manager", e);
        }
    }
}
