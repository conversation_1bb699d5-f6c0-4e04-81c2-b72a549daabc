package net.polyv.service.objectstorage.tx;

import com.google.common.collect.Lists;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.StorageClass;
import com.qcloud.cos.utils.StringUtils;
import lombok.AllArgsConstructor;
import net.polyv.service.objectstorage.UploadOp;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @type AliUploadOpImpl
 * @description
 */
@AllArgsConstructor
public class TxUploadOpImpl extends COSBasicOp implements UploadOp {
    private COSClient client;
    
    private String domain;

    private String bucket;

    /**
     * @param localKey  本地路径，例如 mp4/abc/01.mp4
     * @param remoteKey 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * @description 上传存储对象
     * <AUTHOR>
     */
    @Override
    public Optional<String> upload(String localKey, String remoteKey) {
        final String path = doUpload(localKey,remoteKey,false);

        return Optional.ofNullable(path);
    }

    /**
     * @param inputStream 输入流
     * @param remoteKey   存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * @description 上传存储对象
     * <AUTHOR>
     */
    @Override
    public Optional<String> upload(final InputStream inputStream, final String remoteKey) {
        try {
            client.putObject(bucket, remoteKey, inputStream, null);
            // 查询访问链接
            final String url = super.getUrl(domain, bucket, remoteKey);

            return Optional.ofNullable(url);
        } catch (Exception e) {
            logger.error("upload bucket: {}, remoteKey: {} exception: ", bucket, remoteKey, e);
        }

        return Optional.empty();
    }

    /**
     * @param localKeys  本地路径集合，例如 mp4/abc/01.mp4
     * @param remoteKeys 存储对象路径集合 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * @throws IllegalArgumentException 如果 localKeys 或 remoteKeys 为空，或者两个列表长度不一致，则抛出该异常
     * @description 上传多个存储对象
     * <AUTHOR>
     */
    @Override
    public Optional<List<String>> uploads(final List<String> localKeys, final List<String> remoteKeys) {
        // 参数检查
        check(localKeys, remoteKeys);
        String errorMsg = null;
        final List<String> paths = Lists.newArrayListWithCapacity(localKeys.size());
        for (int i = 0; i < localKeys.size(); i++) {
            final String path = doUpload(localKeys.get(i), remoteKeys.get(i), false);
            if (StringUtils.isNullOrEmpty(path)) {
                // 失败则不再处理后面的
                errorMsg = String.format("failed. localPath=%s, httpPath=%s", localKeys.get(i), remoteKeys.get(i));
                break;
            }
            paths.add(path);
        }

        // errorMsg 为 null 则表示成功
        return errorMsg == null ? Optional.of(paths) : Optional.empty();
    }

    /**
     * @param localKey  本地路径，例如 mp4/abc/01.mp4
     * @param remoteKey 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * @description 上传归档存储对象
     * <AUTHOR>
     */
    @Override
    public Optional<String> uploadArchive(final String localKey, final String remoteKey) {
        final String path = doUpload(localKey, remoteKey, true);
        return Optional.ofNullable(path);
    }

    /**
     * @param inputStream 输入流
     * @param remoteKey   存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * @description 上传归档存储对象
     * <AUTHOR>
     */
    @Override
    public Optional<String> uploadArchive(final InputStream inputStream, final String remoteKey) {
        final String path = doUpload(inputStream, remoteKey, true);
        return Optional.ofNullable(path);
    }

    /**
     * @param localKeys  本地路径集合，例如 mp4/abc/01.mp4
     * @param remoteKeys 存储对象路径集合 | http 路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * @throws IllegalArgumentException 如果 localKeys 或 remoteKeys 为空，或者两个列表长度不一致，则抛出该异常
     * @description 上传归档存储对象
     * <AUTHOR>
     */
    @Override
    public Optional<List<String>> uploadArchives(final List<String> localKeys, final List<String> remoteKeys) {
        // 参数检查
        check(localKeys, remoteKeys);
        String errorMsg = null;
        final List<String> paths = Lists.newArrayListWithCapacity(localKeys.size());
        for (int i = 0; i < localKeys.size(); i++) {
            final String path = doUpload(localKeys.get(i), remoteKeys.get(i), true);
            if (StringUtils.isNullOrEmpty(path)) { // 失败则不再处理后面的
                errorMsg = String.format("failed. localPath=%s, httpPath=%s", localKeys.get(i), remoteKeys.get(i));
                break;
            }
            paths.add(path);
        }

        // errorMsg 为 null 则表示成功
        return errorMsg == null ? Optional.of(paths) : Optional.empty();
    }

    /**
     * @description 上传文件
     * @param localKey 本地KEY
     * @param remoteKey 远程KEY
     * @param archived 是否归档
     * <AUTHOR>
     */
    private String doUpload(final String localKey, final String remoteKey, final boolean archived) {
        final File file = new File(localKey);
        final PutObjectRequest request = new PutObjectRequest(bucket, remoteKey, file);
        try {
            if (archived) {
                // 存储类型-归档
                request.setStorageClass(StorageClass.Archive);
            }
            client.putObject(request);
            // 查询访问链接
            return super.getUrl(domain, bucket, remoteKey);
        } catch (Exception e) {
            logger.error("upload bucket: {}, localKey: {}, remoteKey: {} exception: ", bucket, localKey, remoteKey, e);
        }

        return null;
    }

    /**
     * @description 上传文件
     * @param inputStream 输入流
     * @param remoteKey 远程KEY
     * @param archived 是否归档
     * <AUTHOR>
     */
    private String doUpload(final InputStream inputStream, final String remoteKey, final boolean archived) {
        try {
            final PutObjectRequest request = new PutObjectRequest(bucket, remoteKey, null);
            if (archived) {
                // 存储类型-归档
                request.setStorageClass(StorageClass.Archive);
            }
            request.withInputStream(inputStream);
            client.putObject(request);
            // 查询访问链接
            return super.getUrl(domain, bucket, remoteKey);
        } catch (Exception e) {
            logger.error("upload bucket: {}, remoteKey: {} exception: ", bucket, remoteKey, e);
        }

        return null;
    }

}
