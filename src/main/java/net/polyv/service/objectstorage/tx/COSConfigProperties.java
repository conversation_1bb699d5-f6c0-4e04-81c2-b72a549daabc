package net.polyv.service.objectstorage.tx;

import com.google.common.collect.Maps;
import com.qcloud.cos.http.HttpProtocol;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.polyv.service.objectstorage.SecretKey;

import java.time.Duration;
import java.util.Map;

/**
 * <AUTHOR>
 * @type COSConfigProperties
 * @description 腾讯对象存储-COS配置属性
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(COSConfigProperties.CONFIG_PREFIX)
public class COSConfigProperties extends COSAc {

    // 腾讯云的配置前缀
    public static final String CONFIG_PREFIX = "object.storage.tx";

    // 一个桶对应一个客户端对象，只创建一次 (要提供刷新和重构方法)
    /* KEY: 桶配置名称， VALUE: 客户端配置 */
    private Map<String, ClientProperties> clients = Maps.newHashMap();

    /* KEY: 桶配置名称， VALUE: 存储对象配置 */
    private Map<String, ObjectProperties> objects = Maps.newHashMap();

    /**
     * @description 客户端配置 (appId暂时不支持具体到桶的配置)
     * <AUTHOR>
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static final class ClientProperties extends COSAc {
        // 默认的获取连接的超时时间, 单位ms
        private static final int DEFAULT_CONNECTION_REQUEST_TIMEOUT = -1;

        // 默认连接超时, 单位ms
        private static final int DEFAULT_CONNECTION_TIMEOUT = 30 * 1000;

        // 默认的SOCKET读取超时时间, 单位ms
        private static final int DEFAULT_SOCKET_TIMEOUT = 30 * 1000;

        // 默认的维护最大HTTP连接数
        private static final int DEFAULT_MAX_CONNECTIONS_COUNT = 1024;

        private static final int DEFAULT_IDLE_CONNECTION_ALIVE = 60 * 1000;

        // 多次签名的默认过期时间,单位秒
        private static final long DEFAULT_SIGN_EXPIRED = 3600;

        // Read Limit
        private static final int DEFAULT_READ_LIMIT = (2 << 17) + 1;

        // 桶名称
        private String name;

        // 区域名称参考: https://cloud.tencent.com/document/product/436/6224
        private String region;

        // 访问域名(桶默认或自定义域名)
        private String domain;

        // 端点 | 区域名称参考: https://cloud.tencent.com/document/product/436/6224
        private String endpoint;

        /* http协议，默认https */
        private HttpProtocol httpProtocol = HttpProtocol.https;

        private Long signExpired = DEFAULT_SIGN_EXPIRED;

        private Integer connectionRequestTimeout = DEFAULT_CONNECTION_REQUEST_TIMEOUT;

        private Integer connectionTimeout = DEFAULT_CONNECTION_TIMEOUT;

        private Integer socketTimeout = DEFAULT_SOCKET_TIMEOUT;

        private Integer maxConnectionsCount = DEFAULT_MAX_CONNECTIONS_COUNT;

        private Integer idleConnectionAlive = DEFAULT_IDLE_CONNECTION_ALIVE;

        private String userAgent;

        private Integer readLimit = DEFAULT_READ_LIMIT;
    }

    /**
     * @description 存储对象配置
     * <AUTHOR>
     */
    @Data
    public static final class ObjectProperties {
    
        /* 预签名访问有效时间 */
        private Duration signatureExpire = Duration.ofDays(1);
    
        /* 恢复回档失效天数 */
        private int restoreExpireDays = 1;

    }

}

@Data
abstract class COSAc {

    /* 应用ID，通常作为附加命名 */
    private String appId;

    /*  */
    private String secretId;

    private SecretKey secretKey = new SecretKey();

}