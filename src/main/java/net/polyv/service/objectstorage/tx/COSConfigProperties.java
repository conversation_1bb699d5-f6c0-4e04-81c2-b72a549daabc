package net.polyv.service.objectstorage.tx;

import com.ctrip.framework.apollo.Config;
import com.google.common.collect.Maps;
import com.qcloud.cos.http.HttpProtocol;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.polyv.service.objectstorage.SecretKey;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.Map;

/**
 * <AUTHOR>
 * @type COSConfigProperties
 * @description 腾讯对象存储-COS配置属性
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Component
public class COSConfigProperties extends COSAc {

    // 腾讯云的配置前缀
    public static final String CONFIG_PREFIX = "object.storage.tx";

    // Apollo 配置对象
    private Config apolloConfig;

    // 一个桶对应一个客户端对象，只创建一次 (要提供刷新和重构方法)
    /* KEY: 桶配置名称， VALUE: 客户端配置 */
    private Map<String, ClientProperties> clients = Maps.newHashMap();

    /* KEY: 桶配置名称， VALUE: 存储对象配置 */
    private Map<String, ObjectProperties> objects = Maps.newHashMap();

    /**
     * 构造函数，注入 Apollo Config
     */
    public COSConfigProperties(Config apolloConfig) {
        this.apolloConfig = apolloConfig;
    }

    /**
     * 初始化配置属性
     */
    @PostConstruct
    public void initProperties() {
        // 初始化基础配置
        this.setAppId(apolloConfig.getProperty(CONFIG_PREFIX + ".appId", ""));
        this.setSecretId(apolloConfig.getProperty(CONFIG_PREFIX + ".secretId", ""));

        // 初始化 SecretKey
        SecretKey secretKey = new SecretKey();
        secretKey.setPrivateKey(apolloConfig.getProperty(CONFIG_PREFIX + ".secretKey.privateKey", ""));
        this.setSecretKey(secretKey);

        // 初始化客户端配置
        initClientProperties();

        // 初始化对象配置
        initObjectProperties();
    }

    /**
     * 初始化客户端配置
     */
    private void initClientProperties() {
        // 获取所有客户端配置的键
        String clientsPrefix = CONFIG_PREFIX + ".clients.";

        // 这里需要根据实际的配置键来动态获取
        // 示例：假设配置中有 object.storage.tx.clients.default.* 这样的配置
        String[] clientKeys = getClientKeys(); // 需要实现这个方法来获取所有客户端配置键

        for (String clientKey : clientKeys) {
            ClientProperties clientProps = new ClientProperties();
            String keyPrefix = clientsPrefix + clientKey + ".";

            // 设置客户端属性
            clientProps.setName(apolloConfig.getProperty(keyPrefix + "name", ""));
            clientProps.setRegion(apolloConfig.getProperty(keyPrefix + "region", ""));
            clientProps.setDomain(apolloConfig.getProperty(keyPrefix + "domain", ""));
            clientProps.setEndpoint(apolloConfig.getProperty(keyPrefix + "endpoint", ""));

            // HTTP协议
            String httpProtocol = apolloConfig.getProperty(keyPrefix + "httpProtocol", "https");
            clientProps.setHttpProtocol("https".equals(httpProtocol) ? HttpProtocol.https : HttpProtocol.http);

            // 超时配置
            clientProps.setSignExpired(apolloConfig.getLongProperty(keyPrefix + "signExpired", ClientProperties.DEFAULT_SIGN_EXPIRED));
            clientProps.setConnectionRequestTimeout(apolloConfig.getIntProperty(keyPrefix + "connectionRequestTimeout", ClientProperties.DEFAULT_CONNECTION_REQUEST_TIMEOUT));
            clientProps.setConnectionTimeout(apolloConfig.getIntProperty(keyPrefix + "connectionTimeout", ClientProperties.DEFAULT_CONNECTION_TIMEOUT));
            clientProps.setSocketTimeout(apolloConfig.getIntProperty(keyPrefix + "socketTimeout", ClientProperties.DEFAULT_SOCKET_TIMEOUT));
            clientProps.setMaxConnectionsCount(apolloConfig.getIntProperty(keyPrefix + "maxConnectionsCount", ClientProperties.DEFAULT_MAX_CONNECTIONS_COUNT));
            clientProps.setIdleConnectionAlive(apolloConfig.getIntProperty(keyPrefix + "idleConnectionAlive", ClientProperties.DEFAULT_IDLE_CONNECTION_ALIVE));
            clientProps.setReadLimit(apolloConfig.getIntProperty(keyPrefix + "readLimit", ClientProperties.DEFAULT_READ_LIMIT));
            clientProps.setUserAgent(apolloConfig.getProperty(keyPrefix + "userAgent", ""));

            // 设置秘钥信息（如果客户端级别没有配置，会使用全局配置）
            clientProps.setAppId(apolloConfig.getProperty(keyPrefix + "appId", this.getAppId()));
            clientProps.setSecretId(apolloConfig.getProperty(keyPrefix + "secretId", this.getSecretId()));

            SecretKey clientSecretKey = new SecretKey();
            clientSecretKey.setPrivateKey(apolloConfig.getProperty(keyPrefix + "secretKey.privateKey", this.getSecretKey().getPrivateKey()));
            clientProps.setSecretKey(clientSecretKey);

            clients.put(clientKey, clientProps);
        }
    }

    /**
     * 初始化对象配置
     */
    private void initObjectProperties() {
        String objectsPrefix = CONFIG_PREFIX + ".objects.";
        String[] objectKeys = getObjectKeys(); // 需要实现这个方法来获取所有对象配置键

        for (String objectKey : objectKeys) {
            ObjectProperties objectProps = new ObjectProperties();
            String keyPrefix = objectsPrefix + objectKey + ".";

            // 预签名访问有效时间（天数）
            int signatureExpireDays = apolloConfig.getIntProperty(keyPrefix + "signatureExpireDays", 1);
            objectProps.setSignatureExpire(Duration.ofDays(signatureExpireDays));

            // 恢复回档失效天数
            objectProps.setRestoreExpireDays(apolloConfig.getIntProperty(keyPrefix + "restoreExpireDays", 1));

            objects.put(objectKey, objectProps);
        }
    }

    /**
     * 获取所有客户端配置键
     */
    private String[] getClientKeys() {
        return COSConfigHelper.getClientKeys(apolloConfig, CONFIG_PREFIX);
    }

    /**
     * 获取所有对象配置键
     */
    private String[] getObjectKeys() {
        return COSConfigHelper.getObjectKeys(apolloConfig, CONFIG_PREFIX);
    }

    /**
     * @description 客户端配置 (appId暂时不支持具体到桶的配置)
     * <AUTHOR>
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static final class ClientProperties extends COSAc {
        // 默认的获取连接的超时时间, 单位ms
        public static final int DEFAULT_CONNECTION_REQUEST_TIMEOUT = -1;

        // 默认连接超时, 单位ms
        public static final int DEFAULT_CONNECTION_TIMEOUT = 30 * 1000;

        // 默认的SOCKET读取超时时间, 单位ms
        public static final int DEFAULT_SOCKET_TIMEOUT = 30 * 1000;

        // 默认的维护最大HTTP连接数
        public static final int DEFAULT_MAX_CONNECTIONS_COUNT = 1024;

        public static final int DEFAULT_IDLE_CONNECTION_ALIVE = 60 * 1000;

        // 多次签名的默认过期时间,单位秒
        public static final long DEFAULT_SIGN_EXPIRED = 3600;

        // Read Limit
        public static final int DEFAULT_READ_LIMIT = (2 << 17) + 1;

        // 桶名称
        private String name;

        // 区域名称参考: https://cloud.tencent.com/document/product/436/6224
        private String region;

        // 访问域名(桶默认或自定义域名)
        private String domain;

        // 端点 | 区域名称参考: https://cloud.tencent.com/document/product/436/6224
        private String endpoint;

        /* http协议，默认https */
        private HttpProtocol httpProtocol = HttpProtocol.https;

        private Long signExpired = DEFAULT_SIGN_EXPIRED;

        private Integer connectionRequestTimeout = DEFAULT_CONNECTION_REQUEST_TIMEOUT;

        private Integer connectionTimeout = DEFAULT_CONNECTION_TIMEOUT;

        private Integer socketTimeout = DEFAULT_SOCKET_TIMEOUT;

        private Integer maxConnectionsCount = DEFAULT_MAX_CONNECTIONS_COUNT;

        private Integer idleConnectionAlive = DEFAULT_IDLE_CONNECTION_ALIVE;

        private String userAgent;

        private Integer readLimit = DEFAULT_READ_LIMIT;
    }

    /**
     * @description 存储对象配置
     * <AUTHOR>
     */
    @Data
    public static final class ObjectProperties {
    
        /* 预签名访问有效时间 */
        private Duration signatureExpire = Duration.ofDays(1);
    
        /* 恢复回档失效天数 */
        private int restoreExpireDays = 1;

    }

}

@Data
abstract class COSAc {

    /* 应用ID，通常作为附加命名 */
    private String appId;

    /*  */
    private String secretId;

    private SecretKey secretKey = new SecretKey();

}