package net.polyv.service.objectstorage.tx;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.GetObjectRequest;
import lombok.AllArgsConstructor;
import net.polyv.service.objectstorage.DownloadOp;

import java.io.File;

/**
 * <AUTHOR>
 * @type AliDownloadOpImpl
 * @description
 */
@AllArgsConstructor
public class TxDownloadOpImpl extends COSBasicOp implements DownloadOp {
    private COSClient client;

    private String bucket;

    /**
     * @param remoteKey 存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @param localKey  本地路径，例如 mp4/abc/01.mp4
     * @return true-成功，false-失败
     * @description 下载
     * <AUTHOR>
     */
    @Override
    public boolean download(final String remoteKey, final String localKey) {
        final GetObjectRequest request = new GetObjectRequest(bucket, remoteKey);
        final File file = new File(localKey);
        try {
            // 下载文件
            client.getObject(request, file);
        } catch (Exception e) {
            return false;
        }

        return true;
    }

}
