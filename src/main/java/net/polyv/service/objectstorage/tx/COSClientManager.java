package net.polyv.service.objectstorage.tx;

import com.google.common.collect.Maps;
import com.qcloud.cos.COSClient;
import net.polyv.commons.type.storage.Bucket;
import net.polyv.service.objectstorage.IClientManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @type COSClientManager
 * @description COS客户端管理器
 */
@Component
public class COSClientManager implements IClientManager {

    @Resource
    private COSConfigProperties configProperties;

    /* 桶-客户端工厂 */
    private Map<String, COSClientFactory> clientFactoryMap = Maps.newHashMap();

    /**
     * @description 获取客户端
     * @param bucket 桶
     * @return 客户端
     * <AUTHOR>
     */
    public COSClient getClient(final Bucket bucket) {

        COSClientFactory factory = getClientFactory(bucket) ;
    
        return factory.getClient();
    }

    /**
     * @description 获取客户端
     * @param bucket 桶
     * @return 客户端
     * <AUTHOR>
     */
    public COSClientFactory getClientFactory(final Bucket bucket) {
        final String configKey = getConfigKey(bucket);
        COSClientFactory factory = clientFactoryMap.get(configKey);
        if (null == factory) {
            factory = newFactory(configKey);
            clientFactoryMap.put(configKey, factory);
        }

        return factory ;
    }

    /**
     * @description 重载构造客户端
     * <AUTHOR>
     */
    @Override
    public synchronized void rebuild() {
        final Map<String, COSClientFactory> clientFactoryMap = this.clientFactoryMap;
        this.clientFactoryMap = Maps.newHashMap();
        // 销毁旧的客户端工程
        clientFactoryMap.values().forEach(COSClientFactory:: destroy);
        // 清空客户端缓存
        clientFactoryMap.clear();
    }

    /**
     * @description
     * @param configKey 桶配置名称
     * @return 客户端工厂
     * <AUTHOR>
     */
    private COSClientFactory newFactory(final String configKey) {
        final COSConfigProperties.ClientProperties properties = configProperties.getClients().get(configKey);
        if (null == properties) {
            throw new RuntimeException(String.format("the bucket`s config: %s not exists", configKey));
        }
        // 秘钥设置
        if (StringUtils.isEmpty(properties.getAppId())) {
            properties.setAppId(configProperties.getAppId());
        }
        if (StringUtils.isEmpty(properties.getSecretId())) {
            properties.setSecretId(configProperties.getSecretId());
        }
        if (StringUtils.isEmpty(properties.getSecretKey().getPrivateKey())) {
            properties.setSecretKey(configProperties.getSecretKey());
        }
        return new COSClientFactory(configProperties.getClients().get(configKey));
    }

    /**
     * @description 销毁全部的客户端
     * <AUTHOR>
     */
    @PreDestroy
    @Override
    public synchronized void destroy() {
        clientFactoryMap.values().forEach(COSClientFactory :: destroy);
    }

    /**
     * @param bucket 桶
     * @return 桶名称
     * @description (从配置)获取桶名称
     * <AUTHOR>
     */
    @Override
    public String getName(Bucket bucket) {
        return configProperties.getClients().get(getConfigKey(bucket)).getName();
    }

    /**
     * @description 应用ID
     * @return APPID
     * <AUTHOR>
     */
    String getAppId(final Bucket bucket) {
        String appId = configProperties.getClients().get(getConfigKey(bucket)).getAppId() ;
        return StringUtils.isNotEmpty(appId) ? appId : configProperties.getAppId();
    }

    public String getRegion(final Bucket bucket) {
        return configProperties.getClients().get(getConfigKey(bucket)).getRegion() ;
    }

    /**
     * @description 获取恢复回档失效天数
     * @param bucket 桶
     * @return 恢复回档失效天数
     * <AUTHOR>
     */
    int getExpirationInDays(final Bucket bucket) {
        return configProperties.getObjects().getOrDefault(getConfigKey(bucket), new COSConfigProperties.ObjectProperties()).getRestoreExpireDays();
    }
    
    String getDomain(final Bucket bucket) {
        return configProperties.getClients().getOrDefault(getConfigKey(bucket), new COSConfigProperties.ClientProperties()).getDomain();
    }

}
