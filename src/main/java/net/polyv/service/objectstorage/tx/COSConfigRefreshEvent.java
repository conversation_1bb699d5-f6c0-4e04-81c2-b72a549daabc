package net.polyv.service.objectstorage.tx;

import org.springframework.context.ApplicationEvent;

import java.util.Set;

/**
 * <AUTHOR>
 * @description COS配置刷新事件
 */
public class COSConfigRefreshEvent extends ApplicationEvent {

    private final Set<String> changedKeys;
    private final COSConfigProperties configProperties;

    /**
     * 构造函数
     * 
     * @param source 事件源
     * @param changedKeys 变更的配置键
     * @param configProperties 配置属性对象
     */
    public COSConfigRefreshEvent(Object source, Set<String> changedKeys, COSConfigProperties configProperties) {
        super(source);
        this.changedKeys = changedKeys;
        this.configProperties = configProperties;
    }

    /**
     * 获取变更的配置键
     */
    public Set<String> getChangedKeys() {
        return changedKeys;
    }

    /**
     * 获取配置属性对象
     */
    public COSConfigProperties getConfigProperties() {
        return configProperties;
    }
}
