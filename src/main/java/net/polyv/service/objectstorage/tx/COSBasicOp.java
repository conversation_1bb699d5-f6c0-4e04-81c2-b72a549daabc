package net.polyv.service.objectstorage.tx;

import com.google.common.collect.Lists;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.COSObjectSummary;
import com.qcloud.cos.model.ListObjectsRequest;
import com.qcloud.cos.model.ObjectListing;
import net.polyv.constant.ObjectStorageConstant;
import net.polyv.service.objectstorage.BasicOp;

import java.util.List;

/**
 * <AUTHOR>
 * @type COSBasicOp
 * @description COS基本操作
 */
public abstract class COSBasicOp extends BasicOp {

    
    
    

    /**
     * @description
     * @param bucket 桶
     * @param key 存储对象KEY
     * @return 存储对象地址
     * <AUTHOR>
     */
    protected String getUrl(final String domain, final String bucket, final String key) {
        return String.format("http://%s/%s", bucket + ObjectStorageConstant.DOT_MARK + domain, key);
    }
/*    public String getUrl(final COSClient client, final String bucket, final String key) {
        try {
            final URL url = client.getObjectUrl(bucket, key);

            return null == url ? null : url.toString();
        } catch (Exception e) {
            logger.error("getUrl key: {} exception: ", key, e);
        }

        return null;
    }*/

    /**
     * @description
     * @param client 客户端
     * @param bucket 桶
     * @param prefix 存储对象KEY前缀
     * @return 存储对象KEY列表
     * <AUTHOR>
     */
    public List<String> listByPrefix(final COSClient client, final String bucket, final String prefix) {
        final ListObjectsRequest request = new ListObjectsRequest();
        request.setBucketName(bucket);
        // 设置前缀，列出以指定前缀开头的对象
        request.setPrefix(prefix);
        // / 表示列出当前目录的对象，为空则表示列出所有对象
        //request.setDelimiter("/");
        final ObjectListing listing = client.listObjects(request);
        final List<COSObjectSummary> summaries = listing.getObjectSummaries();
        final List<String> result = Lists.newArrayListWithCapacity(summaries.size());
        summaries.forEach(e -> result.add(e.getKey()));

        return result;
    }

}
