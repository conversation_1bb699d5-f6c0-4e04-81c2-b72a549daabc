package net.polyv.service.objectstorage.tx;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.CopyObjectRequest;
import com.qcloud.cos.region.Region;
import net.polyv.commons.type.storage.Bucket;
import net.polyv.constant.ObjectStorageConstant;
import net.polyv.service.objectstorage.AbstractObjectService;
import net.polyv.service.objectstorage.AccessOp;
import net.polyv.service.objectstorage.BucketConfigProperties;
import net.polyv.service.objectstorage.CopyOp;
import net.polyv.service.objectstorage.DeleteOp;
import net.polyv.service.objectstorage.DownloadOp;
import net.polyv.service.objectstorage.UploadOp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @type TXObjectService
 * @description 腾讯云对象存储COS服务
 */
@Service("txObjectService")
public class TXObjectService extends AbstractObjectService {

    @Resource
    private COSClientManager clientManager;

    /**
     * @description 访问操作
     * @param bucket 桶
     * @return 操作对象
     * <AUTHOR>
     */
    @Override
    public AccessOp boundForAccess(final Bucket bucket) {
        return new TxAccessOpImpl(clientManager.getClient(bucket),
                clientManager.getDomain(bucket), getFormatBucket(bucket),
                clientManager.getExpirationInDays(bucket));
    }

    /**
     * @description 下载操作
     * @param bucket 桶
     * @return 操作对象
     * <AUTHOR>
     */
    @Override
    public DownloadOp boundForDownload(final Bucket bucket) {
        return new TxDownloadOpImpl(clientManager.getClient(bucket), getFormatBucket(bucket));
    }

    /**
     * @description 上传操作
     * @param bucket 桶
     * @return 操作对象
     * <AUTHOR>
     */
    @Override
    public UploadOp boundForUpload(final Bucket bucket) {
        return new TxUploadOpImpl(clientManager.getClient(bucket), clientManager.getDomain(bucket), getFormatBucket(bucket));
    }

    /**
     * @description 删除操作
     * @param bucket 桶
     * @return 操作对象
     * <AUTHOR>
     */
    @Override
    public DeleteOp boundForDelete(final Bucket bucket) {
        return new TxDeleteOpImpl(clientManager.getClient(bucket), getFormatBucket(bucket));
    }

    /**
     * @description 拷贝操作 (同地域复制)
     * @param srcBucket 源桶
     * @return 操作对象
     * <AUTHOR>
     */
    @Override
    public CopyOp boundForCopy(final Bucket srcBucket) {
        return new TxCopyOpImpl(clientManager.getClient(srcBucket), srcBucket, this :: getFormatBucket);
    }

    /**
     * @param sourceKey         源存储对象路径
     * @param destinationBucket 目的桶名称
     * @param destinationKey    目的存储对象路径
     * @param override          是否覆盖 | true-是，false-否
     * @return true-失败，false-成功
     * @description 拷贝
     * <AUTHOR>
     */
    @Override
    public boolean copy(Bucket sourceBucket ,
                        String sourceKey,
                        Bucket destinationBucket,
                        String destinationKey ,
                        boolean override) {
        COSClient client = clientManager.getClient(destinationBucket) ;
        if (!override) { // 不覆盖，判断是否存在， 存在则直接返回
            if (client.doesObjectExist(destinationBucket.getName(), destinationKey)) { // 已存在
                return true;
            }
        }
        final CopyObjectRequest request = new CopyObjectRequest(new Region(clientManager.getRegion(sourceBucket)) ,
                getFormatBucket(sourceBucket),
                sourceKey,
                getFormatBucket(destinationBucket), destinationKey);
        try {
            client.copyObject(request);
        } catch (Exception e) {
            logger.error("copy sourceBucket: {}, sourceKey: {}, destinationBucket: {}, destinationKey:{} exception: ",
                    sourceBucket, sourceKey, destinationBucket, destinationKey, e);
            return false;
        }

        return true;
    }

    /**
     * @description 存储桶的命名格式为 BucketName-APPID，此处填写的存储桶名称必须为此格式
     * @param bucket 桶
     * @return 桶名称
     * <AUTHOR>
     */
    private String getFormatBucket(final Bucket bucket) {
        return clientManager.getName(bucket) + ObjectStorageConstant.MIDDLINE + clientManager.getAppId(bucket);
    }

    /**
     * @description 销毁
     * <AUTHOR>
     */
    @Override
    public void destroy() {
        clientManager.destroy();
    }


    @Override
    public BucketConfigProperties getBucketConfigProperties(Bucket bucket){

        COSClientFactory factory = clientManager.getClientFactory(bucket) ;

        BucketConfigProperties bucketConfigProperties = new BucketConfigProperties() ;
        bucketConfigProperties.setBucketName(bucket.getName() + "-" + factory.getClientProperties().getAppId());
        bucketConfigProperties.setEndpoint(factory.getClientProperties().getEndpoint());
        bucketConfigProperties.setAccessKeyId(factory.getSecretId());
        bucketConfigProperties.setAccessKeySecret(factory.getSecretKey());
        bucketConfigProperties.setDomain(factory.getClientProperties().getDomain());

        return bucketConfigProperties ;
    }

}
