package net.polyv.service.objectstorage.tx;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.CopyObjectRequest;
import lombok.AllArgsConstructor;
import net.polyv.commons.type.storage.Bucket;
import net.polyv.service.objectstorage.CopyOp;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @type AliCopyOpImpl
 * @description
 */
@AllArgsConstructor
public class TxCopyOpImpl extends COSBasicOp implements CopyOp {

    private COSClient client;

    private Bucket sourceBucket;

    private Function<Bucket, String> getFormatBucket;

    /**
     * @param sourceKey         源存储对象路径
     * @param destinationBucket 目的桶名称
     * @param destinationKey    目的存储对象路径
     * @return true-失败，false-成功
     * @description (覆盖)拷贝 | 同地域复制
     * <AUTHOR>
     */
    @Override
    public boolean copy(final String sourceKey, final Bucket destinationBucket, final String destinationKey) {
        return copy(sourceKey, destinationBucket, destinationKey, true);
    }

    /**
     * @param sourceKey         源存储对象路径
     * @param destinationBucket 目的桶名称
     * @param destinationKey    目的存储对象路径
     * @param override          是否覆盖 | true-是，false-否
     * @return true-失败，false-成功
     * @description 拷贝
     * <AUTHOR>
     */
    @Override
    public boolean copy(String sourceKey, Bucket destinationBucket, String destinationKey, boolean override) {
        String sourceBucketName = getFormatBucket.apply(sourceBucket) ;
        String destinationBucketName = getFormatBucket.apply(destinationBucket) ;
        if (!override) { // 不覆盖，判断是否存在， 存在则直接返回
            if (client.doesObjectExist(destinationBucketName, destinationKey)) { // 已存在
                return true;
            }
        }
        final CopyObjectRequest request = new CopyObjectRequest(sourceBucketName,
                sourceKey,destinationBucketName , destinationKey);
        try {
            client.copyObject(request);
        } catch (Exception e) {
            logger.error("copy sourceBucketName: {}, sourceKey: {}, destinationBucketName: {}, destinationKey:{} exception: ",
                    sourceBucketName, sourceKey, destinationBucketName, destinationKey, e);
            return false;
        }

        return true;
    }
}
