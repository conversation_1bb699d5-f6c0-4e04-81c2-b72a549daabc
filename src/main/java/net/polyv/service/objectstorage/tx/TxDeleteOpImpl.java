package net.polyv.service.objectstorage.tx;

import com.qcloud.cos.COSClient;
import lombok.AllArgsConstructor;
import net.polyv.service.objectstorage.DeleteOp;

/**
 * <AUTHOR>
 * @type AliDeleteOpImpl
 * @description
 */
@AllArgsConstructor
public class TxDeleteOpImpl extends COSBasicOp implements DeleteOp {

    private COSClient client;

    private String bucket;

    /**
     * @param key    存储对象路径 | http 路径，例如 mp4/abc/01.mp4
     * @return true-失败，false-成功
     * @description 删除
     * <AUTHOR>
     */
    @Override
    public boolean delete(final String key) {
        try {
            client.deleteObject(bucket, key);
        } catch (Exception e) {
            logger.error("delete object bucket: {}, key: {}, exception: ", bucket, key, e);
            return false;
        }

        return true;
    }
    
}
