package net.polyv.service.objectstorage.tx;

import com.ctrip.framework.apollo.Config;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @description COS配置辅助类，用于从Apollo配置中心解析配置键
 */
public class COSConfigHelper {

    private static final String CLIENTS_PREFIX = ".clients.";
    private static final String OBJECTS_PREFIX = ".objects.";

    /**
     * 从Apollo配置中获取所有客户端配置键
     * 
     * @param config Apollo配置对象
     * @param configPrefix 配置前缀
     * @return 客户端配置键集合
     */
    public static String[] getClientKeys(Config config, String configPrefix) {
        Set<String> clientKeys = new HashSet<>();
        String clientsPrefix = configPrefix + CLIENTS_PREFIX;
        
        // 获取所有配置键
        Set<String> propertyNames = config.getPropertyNames();
        
        for (String propertyName : propertyNames) {
            if (propertyName.startsWith(clientsPrefix)) {
                // 提取客户端键名
                // 例如：object.storage.tx.clients.default.name -> default
                String remaining = propertyName.substring(clientsPrefix.length());
                int dotIndex = remaining.indexOf('.');
                if (dotIndex > 0) {
                    String clientKey = remaining.substring(0, dotIndex);
                    if (StringUtils.isNotBlank(clientKey)) {
                        clientKeys.add(clientKey);
                    }
                }
            }
        }
        
        // 如果没有找到任何客户端配置，返回默认配置
        if (clientKeys.isEmpty()) {
            clientKeys.add("default");
        }
        
        return clientKeys.toArray(new String[0]);
    }

    /**
     * 从Apollo配置中获取所有对象配置键
     * 
     * @param config Apollo配置对象
     * @param configPrefix 配置前缀
     * @return 对象配置键集合
     */
    public static String[] getObjectKeys(Config config, String configPrefix) {
        Set<String> objectKeys = new HashSet<>();
        String objectsPrefix = configPrefix + OBJECTS_PREFIX;
        
        // 获取所有配置键
        Set<String> propertyNames = config.getPropertyNames();
        
        for (String propertyName : propertyNames) {
            if (propertyName.startsWith(objectsPrefix)) {
                // 提取对象键名
                // 例如：object.storage.tx.objects.default.signatureExpireDays -> default
                String remaining = propertyName.substring(objectsPrefix.length());
                int dotIndex = remaining.indexOf('.');
                if (dotIndex > 0) {
                    String objectKey = remaining.substring(0, dotIndex);
                    if (StringUtils.isNotBlank(objectKey)) {
                        objectKeys.add(objectKey);
                    }
                }
            }
        }
        
        // 如果没有找到任何对象配置，返回默认配置
        if (objectKeys.isEmpty()) {
            objectKeys.add("default");
        }
        
        return objectKeys.toArray(new String[0]);
    }

    /**
     * 检查配置键是否存在
     * 
     * @param config Apollo配置对象
     * @param key 配置键
     * @return 是否存在
     */
    public static boolean hasProperty(Config config, String key) {
        try {
            String value = config.getProperty(key, null);
            return value != null;
        } catch (Exception e) {
            return false;
        }
    }
}
