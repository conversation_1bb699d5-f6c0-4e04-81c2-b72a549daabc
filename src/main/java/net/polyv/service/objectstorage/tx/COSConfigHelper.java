package net.polyv.service.objectstorage.tx;

import com.ctrip.framework.apollo.Config;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @description COS配置辅助类，用于从Apollo配置中心解析配置键
 */
public class COSConfigHelper {

    private static final String CLIENTS_PREFIX = ".clients.";
    private static final String OBJECTS_PREFIX = ".objects.";

    /**
     * 从Apollo配置中获取所有客户端配置键
     * 
     * @param config Apollo配置对象
     * @param configPrefix 配置前缀
     * @return 客户端配置键集合
     */
    public static String[] getClientKeys(Config config, String configPrefix) {
        Set<String> clientKeys = new HashSet<>();
        String clientsPrefix = configPrefix + CLIENTS_PREFIX;
        
        // 获取所有配置键
        Set<String> propertyNames = config.getPropertyNames();
        
        for (String propertyName : propertyNames) {
            if (propertyName.startsWith(clientsPrefix)) {
                // 提取客户端键名
                // 例如：object.storage.tx.clients.default.name -> default
                String remaining = propertyName.substring(clientsPrefix.length());
                int dotIndex = remaining.indexOf('.');
                if (dotIndex > 0) {
                    String clientKey = remaining.substring(0, dotIndex);
                    if (StringUtils.isNotBlank(clientKey)) {
                        clientKeys.add(clientKey);
                    }
                }
            }
        }
        
        // 如果没有找到任何客户端配置，返回默认配置
        if (clientKeys.isEmpty()) {
            clientKeys.add("default");
        }
        
        return clientKeys.toArray(new String[0]);
    }

    /**
     * 从Apollo配置中获取所有对象配置键
     * 
     * @param config Apollo配置对象
     * @param configPrefix 配置前缀
     * @return 对象配置键集合
     */
    public static String[] getObjectKeys(Config config, String configPrefix) {
        Set<String> objectKeys = new HashSet<>();
        String objectsPrefix = configPrefix + OBJECTS_PREFIX;
        
        // 获取所有配置键
        Set<String> propertyNames = config.getPropertyNames();
        
        for (String propertyName : propertyNames) {
            if (propertyName.startsWith(objectsPrefix)) {
                // 提取对象键名
                // 例如：object.storage.tx.objects.default.signatureExpireDays -> default
                String remaining = propertyName.substring(objectsPrefix.length());
                int dotIndex = remaining.indexOf('.');
                if (dotIndex > 0) {
                    String objectKey = remaining.substring(0, dotIndex);
                    if (StringUtils.isNotBlank(objectKey)) {
                        objectKeys.add(objectKey);
                    }
                }
            }
        }
        
        // 如果没有找到任何对象配置，返回默认配置
        if (objectKeys.isEmpty()) {
            objectKeys.add("default");
        }
        
        return objectKeys.toArray(new String[0]);
    }

    /**
     * 检查配置键是否存在
     *
     * @param config Apollo配置对象
     * @param key 配置键
     * @return 是否存在
     */
    public static boolean hasProperty(Config config, String key) {
        try {
            String value = config.getProperty(key, null);
            return value != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取配置属性值，支持多种命名格式的兼容性
     * 支持以下格式的自动转换：
     * - camelCase: appId
     * - kebab-case: app-id
     * - snake_case: app_id
     * - dot.case: app.id
     *
     * @param config Apollo配置对象
     * @param keyPrefix 配置前缀
     * @param propertyName 属性名（camelCase格式）
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static String getPropertyWithNamingCompatibility(Config config, String keyPrefix, String propertyName, String defaultValue) {
        // 生成可能的配置键名
        String[] possibleKeys = generatePossibleKeys(keyPrefix, propertyName);

        // 按优先级尝试获取配置值
        for (String key : possibleKeys) {
            String value = config.getProperty(key, null);
            if (value != null) {
                return value;
            }
        }

        return defaultValue;
    }

    /**
     * 获取整型配置属性值，支持多种命名格式的兼容性
     */
    public static int getIntPropertyWithNamingCompatibility(Config config, String keyPrefix, String propertyName, int defaultValue) {
        String[] possibleKeys = generatePossibleKeys(keyPrefix, propertyName);

        for (String key : possibleKeys) {
            try {
                return config.getIntProperty(key, defaultValue);
            } catch (Exception e) {
                // 继续尝试下一个键名
            }
        }

        return defaultValue;
    }

    /**
     * 获取长整型配置属性值，支持多种命名格式的兼容性
     */
    public static long getLongPropertyWithNamingCompatibility(Config config, String keyPrefix, String propertyName, long defaultValue) {
        String[] possibleKeys = generatePossibleKeys(keyPrefix, propertyName);

        for (String key : possibleKeys) {
            try {
                return config.getLongProperty(key, defaultValue);
            } catch (Exception e) {
                // 继续尝试下一个键名
            }
        }

        return defaultValue;
    }

    /**
     * 生成可能的配置键名
     *
     * @param keyPrefix 配置前缀
     * @param propertyName 属性名（camelCase格式）
     * @return 可能的配置键名数组，按优先级排序
     */
    private static String[] generatePossibleKeys(String keyPrefix, String propertyName) {
        String baseKey = keyPrefix.endsWith(".") ? keyPrefix : keyPrefix + ".";

        return new String[] {
            // 1. 原始 camelCase 格式（最高优先级）
            baseKey + propertyName,

            // 2. kebab-case 格式（Spring Boot 推荐格式）
            baseKey + camelToKebab(propertyName),

            // 3. snake_case 格式
            baseKey + camelToSnake(propertyName),

            // 4. dot.case 格式
            baseKey + camelToDot(propertyName),

            // 5. 全小写格式
            baseKey + propertyName.toLowerCase()
        };
    }

    /**
     * 将 camelCase 转换为 kebab-case
     * 例如：appId -> app-id
     */
    private static String camelToKebab(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1-$2").toLowerCase();
    }

    /**
     * 将 camelCase 转换为 snake_case
     * 例如：appId -> app_id
     */
    private static String camelToSnake(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 将 camelCase 转换为 dot.case
     * 例如：appId -> app.id
     */
    private static String camelToDot(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1.$2").toLowerCase();
    }
}
