package net.polyv.service.objectstorage;

import java.util.Map;

/**
 * <AUTHOR>
 * @type UpdateOp
 * @description
 */
public interface UpdateOp {
    
    /**
     * @description 设置存储对象头部数据
     * @param key 源存储对象路径
     * @param name 元数据名称
     * @param value 元数据值
     * @return true-失败，false-成功
     * <AUTHOR>
     */
    boolean setHeader(final String key, final String name, final String value);
    
    /**
     * @description 设置存储对象头部数据
     * @param key 源存储对象路径
     * @param headers 元数据集合
     * @return true-失败，false-成功
     * <AUTHOR>
     */
    boolean setHeaders(final String key, final Map<String, String> headers);
    
}
