package net.polyv.util;

import ayou.util.DOC;
import com.cc.ovp.util.Ext;

public class DOCUtil {

    /**
     * 把对象转换为DOC对象
     */
    public static DOC convert2doc(Object object) {
        return convert2doc(object, false);
    }

    /**
     * 把对象转换为DOC对象
     */
    public static DOC convert2doc(Object object, boolean parseExt) {
        if (object == null) {
            return null;
        }
        DOC doc = JacksonUtil.readValue(JacksonUtil.writeAsString(object), DOC.class);
        if (parseExt) {
            doc = Ext.parseExt(doc);
        }
        return doc;
    }
    
}
