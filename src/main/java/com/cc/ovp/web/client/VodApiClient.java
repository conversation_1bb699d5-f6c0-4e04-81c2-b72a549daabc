package com.cc.ovp.web.client;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.type.TypeReference;
import net.polyv.modules.vod.api.req.screenshot.ScreenshotTaskListReq;
import net.polyv.modules.vod.api.req.screenshot.ScreenshotTaskUpdateStatusReq;
import net.polyv.modules.vod.api.req.screenshot.ScreenshotTaskUpdateDoingStatusReq;
import net.polyv.modules.vod.api.req.screenshot.ScreenshotTaskUndoReq;
import net.polyv.modules.vod.api.vo.VideoPoolVO;
import net.polyv.modules.vod.api.vo.screenshot.ScreenshotTaskVO;
import net.polyv.util.JacksonUtil;
import net.polyv.util.UrlUtil;
import net.polyv.vo.ResponseVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cc.ovp.util.HttpClientUtil;
import com.cc.ovp.util.SignApiUtil;

/**
 * 调用点播API
 * <AUTHOR>
 */
public class VodApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(VodApiClient.class);
    
    private static final String POLYV_DEFAULT_API_INNER = "polyv_default_api_inner";

    private String domain;
    
    private static final VodApiClient INTERNAL_CLIENT;
    
    private static final VodApiClient EXTERNAL_CLIENT;
    
    static {
        INTERNAL_CLIENT = new VodApiClient();
        INTERNAL_CLIENT.domain = "http://api-internal.polyv.net";

        EXTERNAL_CLIENT = new VodApiClient();
        EXTERNAL_CLIENT.domain = "http://api.polyv.net";
    }
    
    private VodApiClient() {
        
    }

    /**
     * 默认使用内网域名
     */
    public static VodApiClient getInstance() {
        return getInstance(false);
    }
    
    public static VodApiClient getInstance(boolean useExternalDomain) {
        return useExternalDomain ? EXTERNAL_CLIENT : INTERNAL_CLIENT;
    }

    /**
     * 自动发布的视频, 可能需要创建智能字幕任务
     */
    public void autoCreateSubtitleTask(String vid) {
        Map<String, String> params = new HashMap<>(4);
        try {
            params.put("vid", vid);
            Map<String, String> reqParams = SignApiUtil.getLiveCommonParams(params, POLYV_DEFAULT_API_INNER);
            String path = "/internal/v3/subtitle/task/auto-create";
            String response = HttpClientUtil.getInstance().sendHttpPost(domain + path, reqParams);
            logger.info("auto create subtitle task, vid: {}, response: {}", vid, response);
        } catch (Exception e) {
            logger.error("auto create subtitle task fail, vid: {}, exception: ", vid, e);
        }
    }
    
    /**
     * 视频截图，刷新CDN缓存
     * @param links 视频截图链接，例如 http://img.videocc.net/uimage/3/36c75a66e1/0/36c75a66e1ff380c878b4232d1621eb0_0.jpg
     */
    public void refreshVideoScreenshotCdnCache(List<String> links) {
        Map<String, String> params = new HashMap<>();
        try {
            params.put("links", String.join(",", links));
            Map<String, String> reqParams = SignApiUtil.getLiveCommonParams(params, POLYV_DEFAULT_API_INNER);
            String path = "/internal/v3/cdn/refresh-screenshot";
            String response = HttpClientUtil.getInstance().sendHttpPost(domain + path, reqParams);
            logger.info("refresh video screenshot cdn cache, links: {}, response: {}", links, response);
        } catch (Exception e) {
            logger.error("refresh video screenshot cdn cache fail, links: {}, exception: ", links, e);
        }
    }
    
    /**
     * 获取截图任务
     */
    public List<ScreenshotTaskVO> getScreenshotTasks(List<String> dones, int size) {
        ScreenshotTaskListReq req = new ScreenshotTaskListReq();
        req.setDones(dones);
        req.setSize(size);
        String body = JacksonUtil.writeAsString(req);
        try {
            String path = "/internal/v3/screenshot-task-v1/get-waiting-tasks";
            String url = appendCommonSignParams(domain + path);
            String response = HttpClientUtil.getInstance().sendHttpPostJsonIgnoreCertVerify(url, body);
            logger.info("get screenshot tasks, req: {}, response: {}", body, response);
            return extractResponseList(response, ScreenshotTaskVO.class);
        } catch (Exception e) {
            logger.error("get screenshot tasks fail, req: {}, exception: ", body, e);
        }
        return null;
    }

    /**
     * 获取截图任务
     */
    public Boolean updateScreenshotTaskStatus(String videoPoolId, String done) {
        ScreenshotTaskUpdateStatusReq req = new ScreenshotTaskUpdateStatusReq();
        req.setDone(done);
        req.setVids(Collections.singletonList(videoPoolId));
        String body = JacksonUtil.writeAsString(req);
        try {
            String path = "/internal/v3/screenshot-task-v1/update-status";
            String url = appendCommonSignParams(domain + path);
            String response = HttpClientUtil.getInstance().sendHttpPostJsonIgnoreCertVerify(url, body);
            logger.info("update screenshot tasks status, req: {}, response: {}", body, response);
            return extractResponseObject(response, Boolean.class);
        } catch (Exception e) {
            logger.error("update screenshot tasks status fail, req: {}, exception: ", body, e);
        }
        return null;
    }

    /**
     * 更新截图任务状态为处理中('I')
     * @param videoPoolIds 视频池ID列表
     * @return 更新记录数
     */
    public Integer updateScreenshotTaskDoingStatus(List<String> videoPoolIds) {
        ScreenshotTaskUpdateDoingStatusReq req = new ScreenshotTaskUpdateDoingStatusReq();
        req.setVideoPoolIds(videoPoolIds);
        String body = JacksonUtil.writeAsString(req);
        try {
            String path = "/internal/v3/screenshot-task-v1/update-doing-status";
            String url = appendCommonSignParams(domain + path);
            String response = HttpClientUtil.getInstance().sendHttpPostJsonIgnoreCertVerify(url, body);
            logger.info("update screenshot tasks doing status, req: {}, response: {}", body, response);
            return extractResponseObject(response, Integer.class);
        } catch (Exception e) {
            logger.error("update screenshot tasks doing status fail, req: {}, exception: ", body, e);
        }
        return null;
    }
    
    /**
     * 撤销截图任务
     * @param videoPoolId 视频池ID
     * @param done 状态
     * @param incrementRetry 是否增加重试次数
     * @return 是否成功
     */
    public Boolean undoScreenshotTask(String videoPoolId, String done, boolean incrementRetry) {
        ScreenshotTaskUndoReq req = new ScreenshotTaskUndoReq();
        req.setVid(videoPoolId);
        req.setDone(done);
        req.setIncrementRetry(incrementRetry);
        String body = JacksonUtil.writeAsString(req);
        try {
            String path = "/internal/v3/screenshot-task-v1/undo-task";
            String url = appendCommonSignParams(domain + path);
            String response = HttpClientUtil.getInstance().sendHttpPostJsonIgnoreCertVerify(url, body);
            logger.info("undo screenshot task, req: {}, response: {}", body, response);
            return extractResponseObject(response, Boolean.class);
        } catch (Exception e) {
            logger.error("undo screenshot task fail, req: {}, exception: ", body, e);
        }
        return null;
    }
    
    /**
     * 更新视频扩展信息
     * @param vid 视频ID
     * @param updateFields 更新字段
     * @return 是否更新成功
     */
    public Boolean updateVideoPoolExt(String vid, LinkedHashMap<String, Object> updateFields) {
        Map<String, Object> req = new HashMap<>();
        req.put("vid", vid);
        req.put("updateFields", updateFields);
        String body = JacksonUtil.writeAsString(req);
        try {
            String path = "/internal/v3/video-pool/update-ext";
            String url = appendCommonSignParams(domain + path);
            String response = HttpClientUtil.getInstance().sendHttpPostJsonIgnoreCertVerify(url, body);
            logger.info("update video pool ext, req: {}, response: {}", body, response);
            return extractResponseObject(response, Boolean.class);
        } catch (Exception e) {
            logger.error("update video pool ext fail, req: {}, exception: ", body, e);
        }
        return null;
    }
    
    /**
     * 获取视频信息
     * @param vid 视频ID
     * @return 视频信息
     */
    public VideoPoolVO getVideoPool(String vid) {
        Map<String, String> params = new HashMap<>();
        try {
            params.put("vid", vid);
            Map<String, String> reqParams = SignApiUtil.getLiveCommonParams(params, POLYV_DEFAULT_API_INNER);
            String path = "/internal/v3/video-pool/get";
            String response = HttpClientUtil.getInstance().sendHttpGet(domain + path, reqParams);
            logger.info("get video pool, vid: {}, response: {}", vid, response);
            return extractResponseObject(response, VideoPoolVO.class);
        } catch (Exception e) {
            logger.error("get video pool fail, vid: {}, exception: ", vid, e);
        }
        return null;
    }

    private <T> T extractResponseObject(String jsonResponse, Class<T> clazz) {
        if (StringUtils.isBlank(jsonResponse)) {
            return null;
        }
        ResponseVO<T> responseVO = JacksonUtil.readValue(jsonResponse, new TypeReference<ResponseVO<T>>() {
        });
        if (responseVO == null || !responseVO.isSuccess()) {
            return null;
        }
        return responseVO.getData();
    }
    

    private <T> List<T> extractResponseList(String jsonResponse, Class<T> clazz) {
        if (StringUtils.isBlank(jsonResponse)) {
            return null;
        }
        ResponseVO<List<T>> responseVO = JacksonUtil.readValue(jsonResponse, new TypeReference<ResponseVO<List<T>>>() {
        });
        if (responseVO == null || !responseVO.isSuccess()) {
            return null;
        }
        return responseVO.getData();
    }
    
    /**
     * 追加timestamp和sign到url后面, post body请求用到 
     */
    private String appendCommonSignParams(String url) {
        Map<String, String> reqParams = SignApiUtil.getLiveCommonParams(Collections.emptyMap(), POLYV_DEFAULT_API_INNER);
        return UrlUtil.appendParams(url, reqParams);
    }
     
}