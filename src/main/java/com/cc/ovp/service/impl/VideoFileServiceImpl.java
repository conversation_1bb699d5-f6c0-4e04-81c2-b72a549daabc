package com.cc.ovp.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cc.ovp.service.VideoFileService;
import com.cc.ovp.util.Config;
import com.cc.ovp.util.LogicUtil;
import com.cc.ovp.vo.AliOssObject;

import ayou.util.DOC;
import net.polyv.constant.Provider;
import net.polyv.huawei.obs.enumeration.OBSBucket;
import net.polyv.huawei.obs.service.OBSService;
import net.polyv.oss.ali.enumeration.AliOssBucket;
import net.polyv.oss.ali.service.AliOssService;
import net.polyv.vod.config.SpecialCDNConfig;

/**
 * 阿里云OSS对象服务
 */
@Service("videoFileService")
public class VideoFileServiceImpl implements VideoFileService {
    
    @Autowired
    private AliOssService aliOssService;
    @Autowired
    private OBSService obsService;
    @Autowired
    private SpecialCDNConfig specialCDNConfig;
    
    @Override
    public String getSourceFileUrl(DOC videoPool) {
        return getSourceFileUrl(videoPool, false, 1000L * 60 * 60);
    }
    
    @Override
    public String getInternalSourceFileUrl(DOC videoPool) {
        return getSourceFileUrl(videoPool, true, 1000L * 60 * 60 * 24 * 2);
    }
    
    // TODO 如果一个视频，原本保持在阿里，后面替换视频，改成保持到华为，那这里会有问题，还是会拿到阿里的文件
    @Override
    public String getSourceFileUrl(DOC videoPool, boolean internal, long validTime) {
        String redirectUrl = videoPool.get("redirect_url");
        if (StringUtils.isNotBlank(redirectUrl)) {
            // ffmpeg不支持https
            return StringUtils.replace(redirectUrl, "https://", "http://");
        }
        Provider provider = Provider.getInstanceByStorageType(videoPool.get("storage_type"));
        AliOssObject ossObject = getAliOssObject(getSrc(videoPool));
        if (aliOssService.opsForAccess().exists(ossObject.getBucket(), ossObject.getHttpPath())) {
            return aliOssService.opsForAccess()
                    .getAccessUrl(ossObject.getBucket(), ossObject.getHttpPath(), internal, validTime).orElse("");
        } else if (obsService.opsForAccess().exists(OBSBucket.getInstance(ossObject.getBucket().getName()), ossObject.getHttpPath())) {
            String result = obsService.opsForAccess().getAccessUrl(OBSBucket.getInstance(ossObject.getBucket().getName()),
                    ossObject.getHttpPath(), validTime).orElse("");
            // ffmpeg不支持https
            return StringUtils.replace(StringUtils.replace(result, "https://", "http://"), ":443", "");
        } else {
            // String result = txObjectService.boundForAccess(COSBucket.getInstance(ossObject.getBucket().getName()))
            //         .getUrl(ossObject.getHttpPath(), Duration.ofMillis(validTime)).orElse("");
            // todo 我们封装的腾讯包不兼容, 这里暂时先不处理
            String result = "";
            return StringUtils.replace(StringUtils.replace(result, "https://", "http://"), ":443", "");
        }
    }
    
    @Override
    public AliOssObject getAliOssObject(String src) {
        if (StringUtils.isEmpty(src)) {
            throw new IllegalArgumentException("src should not be empty");
        }
        
        boolean isSource = src.contains("video_source");
        String userId = StringUtils.substringAfterLast(src, "/").substring(0, 10);
        AliOssBucket bucket;
        String httpPath;
        if (isSource) {
            AliOssBucket userSrcBucket = AliOssBucket.getInstance(specialCDNConfig.getSrcBucket(userId));
            bucket = userSrcBucket != null ? userSrcBucket : AliOssBucket.VOD_VIDEO_SRC;
            httpPath = src.substring(src.indexOf("video_source/"));
        } else {
            AliOssBucket userMpvBucket = AliOssBucket.getInstance(specialCDNConfig.getMpvBucket(userId));
            bucket = userMpvBucket != null ? userMpvBucket : AliOssBucket.VOD_VIDEO_MPV;
            httpPath = "mp4/" + src.substring(src.indexOf("video_target/") + "video_target/".length());
        }
        return new AliOssObject(bucket, httpPath);
    }
    
    
    private String getSrc(DOC videoPool) {
        String hostid = videoPool.get("video_pool_id").substring(0, 1);
        String src;
        if (videoPool.geti("keepsource", 0) == 1) {
            String filename = videoPool.get("swf_link1").split("\\.")[0];
            String sourcefileExt = videoPool.get("swf_link1").split("\\.")[1];
            StringBuffer sourceFileBuffer = new StringBuffer();
            sourceFileBuffer.append(Config.get("htmldir"));
            sourceFileBuffer.append(hostid);
            sourceFileBuffer.append("/video_target/");
            sourceFileBuffer.append(filename + "." + sourcefileExt);
            src = sourceFileBuffer.toString();
        } else {
            // 非源文件上传，使用原文件进行截首图
            src = LogicUtil.getSourceFilePath(videoPool.get("source_file"), hostid);
        }
        return src;
    }
}
