package com.ovp.util;


import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

import net.polyv.modules.vod.api.vo.VideoPoolVO;
import net.polyv.modules.vod.api.vo.screenshot.ScreenshotTaskVO;
import net.polyv.util.DOCUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.cc.ovp.OVPContext;
import com.cc.ovp.dao.DBHelper_db;
import com.cc.ovp.dao.Video_pool_db;
import com.cc.ovp.service.ApolloConfigService;
import com.cc.ovp.service.VideoFileService;
import com.cc.ovp.util.LogicUtil;
import com.cc.ovp.web.client.VodApiClient;

import ayou.util.DOC;
import net.polyv.constant.Provider;
import net.polyv.oss.ali.enumeration.AliOssBucket;
import net.polyv.oss.ali.service.AliOssService;
import net.polyv.vod.config.SpecialCDNConfig;

public class VideoImageQueue {
	
	private static final Logger logger = LoggerFactory.getLogger(VideoImageQueue.class);
	
	private static final String THREAD_NAME_TEMPLATE = "image_handler_%d";
	
	private static final int DEFAULT_THREAD_NUM = 4;
	
	// 控制进程运行状态的文件，文件内容为 quit 则进程退出，为其它值或者文件不存在，则进程继续运行
	private static final String EXIT_SIGNAL_FILE_PATH = "/home/<USER>/polyv_video_image_queue_status";
	
	private static volatile boolean isTest = false;

	/**
	 * 是否是 LOCAL 模式 (LOCAL模式目前是腾讯环境下在使用, 不支持内网, 不支持连接数据库redis) 
	 */
	private static volatile boolean isLocalEnv = false;
	
	private final ApplicationContext applicationContext;
	
	private final ApolloConfigService apolloConfigService;

	/**
	 * LOCAL 模式下这个 helper 是null
	 */
	private final DBHelper_db helper;
	
	// 工作队列
	private BlockingQueue<DOC> undoQueue;
	// 程序安全退出锁
	private final CountDownLatch shutdownLath;
	public static final int QUEUE_LIMIT = 20; // 队列中的任务数达到该值时，则暂停塞任务到队列中

	public VideoImageQueue(int queueSzie) {
		applicationContext = new ClassPathXmlApplicationContext("classpath:applicationContext_base.xml");
		this.apolloConfigService = applicationContext.getBean(ApolloConfigService.class);
		undoQueue = new LinkedBlockingDeque<DOC>(queueSzie);
		shutdownLath = new CountDownLatch(queueSzie+1);
		if (!isLocalEnv) {
			helper = new DBHelper_db();
		} else {
			helper = null;
		}
	}
	
	public void makejob(int threadNum) {
		AliOssService aliOssService = (AliOssService) OVPContext.getBean("aliOssService");
		VideoFileService videoFileService = (VideoFileService) applicationContext.getBean("videoFileService");
		SpecialCDNConfig specialCDNConfig = applicationContext.getBean(SpecialCDNConfig.class);
		
		for (int i = 0; i < threadNum; i++) {
			new Thread(new VideoImageHandler(aliOssService, videoFileService, specialCDNConfig),
					String.format(THREAD_NAME_TEMPLATE, i)).start();
		}
	}
	
	/**
	 * 截图程序启动方法
	 * 如：/bin/sh /home/<USER>/polyv/release-current/webapp/WEB-INF/sh/runjava.sh "com.ovp.util.VideoImageQueue 4 test"
	 * 启动4个线程的测试模式(runjava.sh只接受一个参数，所以以字符串形式传入)
	 * @param args 第一个参数为线程数量，第二个参数为测试模式
	 */
	public static void main(String[] args) {
		logger.info("got args: " + Arrays.toString(args));
		if ("LOCAL".equalsIgnoreCase(System.getenv("ENV"))) {
			isLocalEnv = true;
			logger.info("run with local env model...");
		}
		int threadNum = DEFAULT_THREAD_NUM;
		if (args.length >= 1) {
			try {
				threadNum = Integer.parseInt(args[0]);
			} catch (NumberFormatException e) {
				logger.error(String.format("parse threadNum error, use default: %s", DEFAULT_THREAD_NUM));
			}
		}
		VideoImageQueue t = new VideoImageQueue(threadNum);
		if (args.length >= 2) {
			isTest = "test".equalsIgnoreCase(args[1]);
		}
		Provider[] storageProviders = t.apolloConfigService.getStorageProviders();
		logger.info("run mode: test=" + isTest + ", storageProviders=" + Arrays.toString(storageProviders));
		
		t.makejob(threadNum);
		t.queryDataFromDBAdd2Queue();
		t.destory();
	}
	
	/**
	 * 清理工作
	 */
	private void destory() {
		try {
			shutdownLath.await();
		} catch (InterruptedException e) {
			logger.error(e.getMessage(), e);
		}
		logger.info("exit begin, start clean up");
		for (DOC e=undoQueue.poll(); e!=null; e=undoQueue.poll()) {
			logger.info("reset vid: " + e.get("videoPoolId"));
			undoVideoImageQueue(e, false);
		}
		logger.info("exit end, finish clean up");
		System.exit(0);
	}
	
	private void undoVideoImageQueue(DOC videoImageQueue, boolean incrementRetry) {
		Provider storageProvider = (Provider) videoImageQueue.geto("storage_type");
		String videoPoolId = videoImageQueue.get("videoPoolId");
		char done = storageProvider.getVideoImageQueueInit();
		if (isTest) {
			done = 'T';
		}
		if (!isLocalEnv) {
			helper.undoVideoImageQueue(videoPoolId, done, incrementRetry);
			return;
		}
		VodApiClient.getInstance(isLocalEnv).undoScreenshotTask(videoPoolId, String.valueOf(done), incrementRetry);
	}

	private class VideoImageHandler implements Runnable {
		private AliOssService aliOssService;
		private VideoFileService videoFileService;
		private SpecialCDNConfig specialCDNConfig;
		
		public VideoImageHandler(AliOssService aliOssService, VideoFileService videoFileService,
				SpecialCDNConfig specialCDNConfig) {
			this.aliOssService = aliOssService;
			this.videoFileService = videoFileService;
			this.specialCDNConfig = specialCDNConfig;
		}
		
		@Override
		public void run() {
			while (true) {
				if (getExitSignal()) {
					logger.info("got exit signal, exit current thread now");
					shutdownLath.countDown();
					break;
				}
				screenshot();
			}
		}

		private void screenshot() {
			DOC doc = null;
			try {
				doc = undoQueue.poll(5, TimeUnit.SECONDS);
			} catch (InterruptedException e) {
				// 恢复中断
				Thread.interrupted();
				logger.error(e.getMessage(), e);
			}
			
			if (doc == null) {
				return;
			}
			
			try {
				screenshot(doc);
			} catch (Exception e) {
				logger.error(String.format("cut image error:%s", doc.get("videoPoolId")), e);
				retryScreenshotIfNeed(doc);
			}
		}

		private boolean screenshot(DOC videoImageQueue) {
			String videoPoolId = videoImageQueue.get("videoPoolId");
			logger.info("get vid:" + videoPoolId);
			String hostid = videoPoolId.substring(0, 1);
			DOC video_pool = getVideoPool(videoPoolId);
			String imgsr = getFirstImage(video_pool, hostid);
			AliOssBucket userBucket = Optional.ofNullable(AliOssBucket.getInstance(specialCDNConfig.getImgBucket(video_pool.get("userid"))))
					.orElse(AliOssBucket.VOD_IMG);
			
			boolean isChangeVideo = "1".equals(video_pool.get("videoChanged"));
			boolean hasImage = !isChangeVideo && existsOss(userBucket, imgsr);

			boolean fromLive = "1".equals(video_pool.get("fromLive"));
			int cover_images_count = fromLive?6:1; //直播转存需要6张图 http://pm.igeeker.org/browse/LIVE-39951


			if (!hasImage) {
				String sourceFileUrl = getFileUrl(video_pool);
				int INWIDTH = Integer.parseInt(video_pool.get("original_definition").split("x")[0]);
				int INHEIGHT = Integer.parseInt(video_pool.get("original_definition").split("x")[1]);
				if (!isLocalEnv) {
					LogicUtil.cutImage(hostid, videoPoolId, cover_images_count, sourceFileUrl, INWIDTH, INHEIGHT);
				} else {
					LinkedHashMap<String, Object> extMap = LogicUtil.cutImage(video_pool, cover_images_count, sourceFileUrl, INWIDTH, INHEIGHT);
					if (extMap == null || extMap.isEmpty()) {
						return false;
					}
					VodApiClient.getInstance(isLocalEnv).updateVideoPoolExt(videoPoolId, extMap);
				}

				// 重新查询是否有图片
				video_pool = getVideoPool(videoPoolId);
				imgsr = getFirstImage(video_pool, hostid);
				hasImage = existsOss(userBucket, imgsr);
			}

			if (hasImage && userBucket!=AliOssBucket.SGU_VOD_IMG) {
				cleanCDNIfVideoChanged(video_pool);
			}

			if (!hasImage && !imgsr.contains("default")) {
				retryScreenshotIfNeed(videoImageQueue);
				return false;
			}
			logger.info("image cut done." + videoPoolId);
			finishVideoImageQueue(videoPoolId);
			return true;
		}
		
		private DOC getVideoPool(String videoPoolId) {
			if (!isLocalEnv) {
				Video_pool_db vpdb = new Video_pool_db(videoPoolId.substring(0, 1));
				DOC video_pool = vpdb.getOne(videoPoolId);
				try {
					vpdb.conn.close();
				} catch (Exception e) {
					logger.error(e.getMessage(), e);
				}
				return video_pool;
			}
			VideoPoolVO videoPool = VodApiClient.getInstance(isLocalEnv).getVideoPool(videoPoolId);
			return DOCUtil.convert2doc(videoPool, true);
		}

		private void retryScreenshotIfNeed(DOC videoImageQueue) {
			String videoPoolId = videoImageQueue.get("videoPoolId");
			int retryCount = videoImageQueue.geti("retry");
			logger.info("image cut failed retry.." + videoPoolId);
			if (retryCount > 1) {
				logger.info("image cut failed 2 times.." + videoPoolId);
				failVideoImageQueue(videoPoolId);
				return;
			}
			retryVideoImageQueue(videoImageQueue);
		}

		/**
		 * 获取首图
		 *
		 * @param video_pool video_pool
		 * @param hostid 分表键
		 * @return 若无返回"", 有返回真实首图链接
		 */
		private String getFirstImage(DOC video_pool, String hostid) {
			String first_image = video_pool.get("first_image");
			if (StringUtils.isBlank(first_image)) return "";
			return StringUtils.substring(LogicUtil.parseUrl("image", hostid) + first_image, 1);
		}
		
		/**
		 * 指定路径是否存在文件在oss
		 * @param bucket 桶名
		 * @param url 指定路径
		 * @return true-存在，false-不存在
		 */
		private boolean existsOss(AliOssBucket bucket, String url) {
			if (StringUtils.isBlank(url)) return false;
			return aliOssService.opsForAccess().exists(bucket, url);
		}

		private String getFileUrl(DOC videoPool) {
			return videoFileService.getInternalSourceFileUrl(videoPool);
		}

	}
	
	/**
	 * 替换视频且是源文件播放的情况，清理CDN缓存
	 */
	private void cleanCDNIfVideoChanged(DOC doc) {
		String video_pool_id = doc.get("video_pool_id");
		if (1 == doc.geti("keepsource") && doc.geti("videoChanged") == 1) {
			logger.info("clean cdn for video changed, videoPoolId={}", video_pool_id);
			String hostid = video_pool_id.substring(0, 1);
			// 替换源文件的视频，只有一个码率，可以在重新截图后就主动清理CDN缓存。编码的多码率在编码侧统一处理
			String first_image_b = LogicUtil.parseUrl("image", hostid, doc.get("first_image_b"), "http", doc);
			String first_image = LogicUtil.parseUrl("image", hostid, doc.get("first_image"), "http", doc);
			try {
				VodApiClient.getInstance(isLocalEnv).refreshVideoScreenshotCdnCache(Arrays.asList(first_image, first_image_b));
			} catch (Exception e) {
				logger.error("failed to clean cdn for video changed, videoPoolId={}", video_pool_id, e);
			}
			
			// TODO 为什么在这里做视频预热？不确定其用途，暂时停掉了
//			aliPushService.pushVideoAsync(video_pool_id + "_" + hostid, 1, SegmentType.BOTH);
		}
	}

	/**
	 * 从数据库查询数据添加到未工作队列
	 */
	private void queryDataFromDBAdd2Queue() {
		while (true) {
			if (getExitSignal()) {
				logger.info("got exit signal, exit query thread now");
				shutdownLath.countDown();
				break;
			}
			DOC[] docs = null;
			
			// 如果队列长度达到阈值，则暂不继续塞任务，暂停10秒
			if (undoQueue.size() >= QUEUE_LIMIT) {
				try {
					TimeUnit.SECONDS.sleep(10L);
				} catch (InterruptedException e) {
					logger.error(e.getMessage(), e);
				}
				continue;
			}

			int limit = QUEUE_LIMIT - undoQueue.size();
			try {
				docs = getTasks(limit);
			} catch (Exception e) {
				// 防止异常结束了主线程
				logger.error(e.getMessage(), e);
			}
			
			if (ArrayUtils.isEmpty(docs)) {
				// 查不到数据，睡眠10秒
				try {
					logger.info("no task, sleep 10 seconds");
					TimeUnit.SECONDS.sleep(10L);
				} catch (InterruptedException e) {
					// ignore
				}
				continue;
			}

			fillVideoImageQueueStorageType(docs);
			
			updateVideoImageQueueDoing(getVideoPoolIds(docs));
			
			for (DOC doc : docs) {
				try {
					undoQueue.put(doc);
				} catch (InterruptedException e) {
					// ignore
					Thread.interrupted();
				}
			}
		}
	}
	
	/**
	 * 更新状态为处理中
	 */
	private void updateVideoImageQueueDoing(String[] videoPoolIds) {
		if (!isLocalEnv) {
			helper.updateVideoImageQueueDoing(videoPoolIds);
		} else {
			VodApiClient.getInstance(isLocalEnv).updateScreenshotTaskDoingStatus(Arrays.asList(videoPoolIds));
		}
	}

	private DOC[] getTasks(int limit) {
		if (!isLocalEnv) {
			if (isTest) {
				return helper.getVideoImageQueueTest(0, limit);
			} else {
				return helper.getVideoImageQueue(apolloConfigService.getStorageProviders(), 0, limit);
			}
		}

		// API 模式获取任务
		List<String> dones = new ArrayList<>();
		if (isTest) {
			dones.add("T");
		} else {
			dones.add(String.valueOf(Provider.TX.getVideoImageQueueInit()));
		}
		List<ScreenshotTaskVO> screenshotTasks = VodApiClient.getInstance(isLocalEnv).getScreenshotTasks(dones, limit);
		if (CollectionUtils.isEmpty(screenshotTasks)) {
			return null;
		}
		DOC[] docs = new DOC[screenshotTasks.size()];
		for (int i = 0; i < screenshotTasks.size(); i++) {
			ScreenshotTaskVO screenshotTaskVO = screenshotTasks.get(i);
			docs[i] = DOCUtil.convert2doc(screenshotTaskVO);
		}
		return docs;
	}
	
	private DOC[] fillVideoImageQueueStorageType(DOC[] docs) {
		if (ArrayUtils.isEmpty(docs)) {
			return docs;
		}
		for (DOC d : docs) {
			int done = d.geti("done", Provider.AB.getVideoImageQueueInit());
			if (done == Provider.HW.getVideoImageQueueInit()) {
				d.put("storage_type", Provider.HW);
			} else if (done == Provider.TX.getVideoImageQueueInit()) {
				d.put("storage_type", Provider.TX);
			} else {
				d.put("storage_type", Provider.AB);
			}
		}
		return docs;
	}
	
	private void failVideoImageQueue(String videoPoolId) {
		try {
			if (!isLocalEnv) {
				helper.failVideoImageQueue(videoPoolId);
			} else {
				VodApiClient.getInstance(isLocalEnv).updateScreenshotTaskStatus(videoPoolId, "F");
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}
	
	private void retryVideoImageQueue(DOC videoImageQueue) {
		undoVideoImageQueue(videoImageQueue, true);
	}
	
	private void finishVideoImageQueue(String videoPoolId) {
		try {
			if (!isLocalEnv) {
				helper.finishVideoImageQueue(videoPoolId);
			} else {
				VodApiClient.getInstance(isLocalEnv).updateScreenshotTaskStatus(videoPoolId, "Y");
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
	}

	private String[] getVideoPoolIds(DOC[] docs) {
		String[] videoPoolIds = new String[docs.length];
		for (int i = 0; i < docs.length; i++) {
			videoPoolIds[i] = docs[i].get("videoPoolId");
		}
		return videoPoolIds;
	}

	/**
	 * 获取进程退出信号
	 * @return 当退出信号文件存在，且文件内容为 quit 时，返回 true。否则，返回 false。
	 */
	private static boolean getExitSignal() {
		try {
			File file = new File(EXIT_SIGNAL_FILE_PATH);
			if (file.exists()) {
				return "quit".equalsIgnoreCase(FileUtils.readFileToString(file).trim());
			}
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
		}
		return false;
	}

}
