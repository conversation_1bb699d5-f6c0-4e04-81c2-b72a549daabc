<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- 配置 mongo -->
	<bean id="mongo" class="com.mongodb.Mongo">
		<constructor-arg type="String" value="${vod.mongodb.host}"></constructor-arg>
		<constructor-arg type="int" value="${vod.mongodb.port}"></constructor-arg>
	</bean>

	<bean id="morphia" class="com.google.code.morphia.Morphia"></bean>

	<bean id="mongoDatastore" class="com.cc.ovp.ds.MongoDataStore" init-method="init">
		<constructor-arg ref="morphia" />
		<!-- Mongo连接器 -->
		<constructor-arg ref="mongo" />
		<!-- 数据库名 -->
		<constructor-arg value="${vod.mongodb.database}" />
		<!-- 用户名 -->
		<constructor-arg value="${vod.mongodb.username}" />
		<!-- 密码 -->
		<constructor-arg value="${vod.mongodb.password}" />
		<property name="entityClasses">
			<list>
				<value>com.cc.ovp.domain.entity.DownLoadVideoEntity</value>
				<value>com.cc.ovp.domain.entity.VideoForSearchEntity</value>
			</list>
		</property>
	</bean>

</beans>