<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd">

    <!-- 配置支持注解方式的Handler -->
    <bean class="org.springframework.web.servlet.mvc.annotation.DefaultAnnotationHandlerMapping"/>
    <!-- 配置支持注解方式的Method Handler -->
    <bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter"/>

    <context:annotation-config/>

    <!-- Configures the @Controller programming model -->
    <mvc:annotation-driven/>

    <!--
    配置支持内容协商的视图解析器:
    内容协商: 当服务一个请求时选择资源的一种适当的表示形式的机制.
    例如 /customer/1.json 响应id为1的customer信息的JSON形式的内容;
        /customer/1.xml 响应id为1的customer信息的XML形式的内容.
    -->
    <bean class="org.springframework.web.servlet.view.ContentNegotiatingViewResolver">
        <!-- 忽略对Accept Header的支持 -->
        <property name="ignoreAcceptHeader" value="true"/>
        <!-- 配置参数方式的支持-->
        <property name="favorParameter" value="true"/>
        <!-- 配置参数方式使用的参数名称 例如/xxx?_format=json -->
        <property name="parameterName" value="_format"/>
        <property name="defaultContentType" value="text/html"/>
        <property name="mediaTypes">
            <map>
                <!-- 配置mimeType的映射, 即 /xxx.json: application/json -->
                <entry key="json" value="application/json"/>
            </map>
        </property>
        <!-- 视图解析器 -->
        <property name="viewResolvers">
            <list>
                <bean class="org.springframework.web.servlet.view.BeanNameViewResolver"/>
                <bean class="org.springframework.web.servlet.view.velocity.VelocityViewResolver">
                    <property name="exposeSessionAttributes" value="true"/>
                    <property name="cache" value="true"/>
                    <property name="prefix" value="/WEB-INF/views/"/>
                    <property name="suffix" value=".vm"/>
                    <property name="contentType" value="text/html;charset=UTF-8"/>
                    <property name="toolboxConfigLocation" value="/WEB-INF/classes/velocitytoolbox.xml"/>
                </bean>
                <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
                    <property name="prefix" value="/WEB-INF/views/"/>
                    <property name="suffix" value=".jsp"/>
                </bean>
            </list>
        </property>
        <!-- 默认视图 -->
        <property name="defaultViews">
            <list>
                <bean class="org.springframework.web.servlet.view.json.MappingJacksonJsonView"></bean>
            </list>
        </property>
    </bean>
    <!-- 配置国际化资源文件路径 -->
    <bean id="messageSource" class="org.springframework.context.support.ResourceBundleMessageSource">
        <property name="basename">
            <!-- 定义消息资源文件的相对路径 -->
            <value>message</value>
        </property>
    </bean>
    <bean id="localeResolver" class="org.springframework.web.servlet.i18n.CookieLocaleResolver">
        <property name="cookieMaxAge" value="604800"/>
        <property name="defaultLocale" value="zh_CN"/>
        <property name="cookieName" value="lang"/>
    </bean>
    <bean id="jacksonMessageConverter"
          class="org.springframework.http.converter.json.MappingJacksonHttpMessageConverter">
        <!-- 解决 HttpMediaTypeNotAcceptableException: Could not find acceptable representation -->
        <property name="supportedMediaTypes">
            <list>
                <value>application/json;charset=UTF-8</value>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
        <property name="messageConverters">
            <list>
                <ref bean="jacksonMessageConverter"/>
            </list>
        </property>
    </bean>

    <!-- 配置文件上传, 这里使用apache的CommonsFileUpload -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="999999999999999"/>
    </bean>

    <!-- 配置velocity模板引擎-->
    <bean id="velocityConfig" class="org.springframework.web.servlet.view.velocity.VelocityConfigurer">
        <property name="configLocation" value="classpath:velocity.properties"/>
        <property name="overrideLogging" value="false"/>
    </bean>

    <!-- 配置拦截器 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.cc.ovp.web.interceptor.RequestIdInterceptor"></bean>
        </mvc:interceptor>
        <mvc:interceptor>
            <mvc:mapping path="/*"/>
            <bean class="com.cc.ovp.web.interceptor.UserLoginInterceptor">
                <property name="excludes">
                    <array>
                        <value>/uc/relogin</value>
                        <value>/uc/agent/logining</value>
                        <value>/uc/agent/login</value>
                        <value>/uc/user/login</value>
                        <value>/uc/user/salogin</value>
                        <value>/uc/user/regedit</value>
                        <value>/uc/logout</value>
                        <value>/uc/video/settings/xml</value>
                        <value>/uc/video/settings/mdcode</value>
                        <value>/uc/video/settings/userxml</value>
                        <value>/uc/video/settings/nearxml</value>
                        <value>/uc/video/settings/playlistvideos</value>
                        <value>/uc/video/settings/recommendvideo</value>
                        <value>/uc/video/preview</value>
                        <value>/services/rest</value>
                        <value>/uc/services/rest</value>
                        <value>/uc/services/getfile</value>
                        <value>/uc/services/getfilepath</value>
                        <value>/uc/services/getrealpath</value>
                        <value>/uc/services/recv</value>
                        <value>/uc/services/getpic</value>
                        <value>/uc/services/delcachefile</value>
                        <value>/uc/services/editpool_post_out</value>
                        <value>/uc/video/getMp4</value>
                        <value>/uc/video/downloadMp4</value>
                        <value>/uc/task/start</value>
                        <value>/uc/task/taskday</value>
                        <value>/uc/video/videocheck</value>
                        <value>/uc/user/salescheck</value>
                        <value>/uc/adv/advcode</value>
                        <value>/mp4</value>
                        <value>/flv</value>
                        <!-- 交互视频问答 -->
                        <value>/uc/exam/get</value>
                        <value>/uc/examlog/save</value>

                        <!-- 健康状态管理 -->
                        <value>/manage/v1/health/get-status</value>
                        <value>/manage/v1/health/set-status</value>
                        <value>/uc/kaptcha1.jpg</value>
                        <value>/uc/job/</value>
                        <value>/uc/internal/video/src-url</value>
                    </array>
                </property>
            </bean>
        </mvc:interceptor>
        <mvc:interceptor>
            <mvc:mapping path="/orders/**"/>
            <bean class="com.cc.ovp.web.interceptor.UserLoginInterceptor">
            </bean>
        </mvc:interceptor>

        <mvc:interceptor>
            <mvc:mapping path="/*"/>
            <bean class="com.cc.ovp.web.NoCacheInterceptor">
            </bean>
        </mvc:interceptor>
        <mvc:interceptor>
            <mvc:mapping path="/admin_cc/*"/>
            <bean class="com.cc.ovp.web.interceptor.AdminLoginInterceptor">
                <property name="excludes">
                    <array>
                        <value>/uc/admin_cc/videomanagelist</value>
                        <value>/uc/admin_cc/videomanage</value>
                        <value>/uc/admin_cc/main</value>
                        <value>/uc/admin_cc/frame</value>
                        <value>/uc/admin_cc/top</value>
                        <value>/uc/admin_cc/left</value>
                        <value>/uc/admin_cc/welcome</value>
                    </array>
                </property>
            </bean>
        </mvc:interceptor>
        <mvc:interceptor>
            <mvc:mapping path="/admin_cc/*/*"/>
            <bean class="com.cc.ovp.web.interceptor.AdminLoginInterceptor">
                <property name="excludes">
                    <array>
                        <value>/uc/user/login</value>
                        <value>/uc/user/regedit</value>
                        <value>/uc/logout</value>
                        <value>/uc/kaptcha1.jpg</value>
                    </array>
                </property>
            </bean>
        </mvc:interceptor>

        <!-- 任务拦截器 -->
        <mvc:interceptor>
            <mvc:mapping path="/job/**"/>
            <bean class="net.polyv.web.interceptor.JobInterceptor"/>
        </mvc:interceptor>

        <!-- v2 API 签名拦截器 -->
        <mvc:interceptor>
            <mvc:mapping path="/v2/**"/>
            <bean class="net.polyv.web.interceptor.ApiSignV2Interceptor"/>
        </mvc:interceptor>

        <!-- <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.cc.ovp.web.interceptor.BannedIpInterceptor">
            </bean>
        </mvc:interceptor>
         -->

    </mvc:interceptors>

    <!-- 配置异常处理
    <bean class="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver">
        <property name="exceptionAttribute" value="exception"/>
        <property name="exceptionMappings">
            <props>
                <prop key="java.lang.Throwable">/error/500</prop>
            </props>
        </property>
    </bean>
     -->
    <bean id="sessionManager" class="com.cc.ovp.web.sna.SessionManager">
        <property name="handler">
            <bean class="com.cc.ovp.web.sna.RedisSessionHandler">
                <property name="cookieName" value="ovp-sessionid"/>
                <property name="cookieDomain" value=".polyv.net"/>
                <property name="cookiePath" value="/"/>
                <property name="cookieMaxAge" value="0"/><!-- 浏览器关闭 -->
            </bean>
        </property>
    </bean>
    <!-- 自动扫描组件 -->
    <context:component-scan base-package="com.cc.ovp.web"/>
    <context:component-scan base-package="com.cc.AgentPlatform.web"/>
    <context:component-scan base-package="net.polyv.web"/>

    <!-- 配置静态文件(js,css,html...), 仅作示例, 一般servlet容器不应响应静态文件, 而是由apache或nginx等处理 -->

    <mvc:resources mapping="/js/**" location="/js/"/>
    <mvc:resources mapping="/css/**" location="/css/"/>
    <mvc:resources mapping="/upload/**" location="/upload/"/>
    <mvc:resources mapping="/img/**" location="/img/"/>

</beans>
