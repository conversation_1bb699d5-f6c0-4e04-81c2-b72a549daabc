#----------------------------------------------------------------------------
# These are the default properties for the
# Velocity Runtime. These values are used when
# Runtime.init() is called, and when Runtime.init(properties)
# fails to find the specificed properties file.
#----------------------------------------------------------------------------

#----------------------------------------------------------------------------
# R U N T I M E  L O G
#----------------------------------------------------------------------------

#----------------------------------------------------------------------------
#  default LogSystem to use: default: AvalonLogSystem
#----------------------------------------------------------------------------

runtime.log.logsystem.class = org.apache.velocity.runtime.log.AvalonLogSystem,org.apache.velocity.runtime.log.SimpleLog4JLogSystem

#---------------------------------------------------------------------------
# This is the location of the Velocity Runtime log.
#----------------------------------------------------------------------------

runtime.log = velocity.log

#----------------------------------------------------------------------------
# This controls if Runtime.error(), info() and warn() messages include the
# whole stack trace. The last property controls whether invalid references
# are logged.
#----------------------------------------------------------------------------

runtime.log.error.stacktrace = false
runtime.log.warn.stacktrace = false
runtime.log.info.stacktrace = false
runtime.log.invalid.reference = true

#----------------------------------------------------------------------------
# Configuration for the Log4JLogSystem.
# You must define the runtime.log.logsystem.class property to be:
#   org.apache.velocity.runtime.log.Log4JLogSystem
#
# You must also include Log4J's .jar files into your classpath. They are
# included with the Velocity distribution in the build/lib directory.
#
# There are several different options that you can configure.
# Uncomment the ones that you want and also define their settings.
#----------------------------------------------------------------------------
#runtime.log.logsystem.log4j.pattern=%d - %m%n
#runtime.log.logsystem.log4j.file.size=100000
#runtime.log.logsystem.log4j.file.backups=1
#runtime.log.logsystem.log4j.syslogd.host=my.syslog.server.com
#runtime.log.logsystem.log4j.syslogd.facility=LOG_DAEMON
#runtime.log.logsystem.log4j.remote.host=my.remote.server.com
#runtime.log.logsystem.log4j.remote.port=1099
#runtime.log.logsystem.log4j.email.server=localhost
#runtime.log.logsystem.log4j.email.from=root@localhost
#runtime.log.logsystem.log4j.email.to=root@localhost
#runtime.log.logsystem.log4j.email.subject=Velocity Error Report
#runtime.log.logsystem.log4j.email.buffer.size=512

#----------------------------------------------------------------------------
# T E M P L A T E  E N C O D I N G
#----------------------------------------------------------------------------

input.encoding=UTF-8
output.encoding=UTF-8

#----------------------------------------------------------------------------
# F O R E A C H  P R O P E R T I E S
#----------------------------------------------------------------------------
# These properties control how the counter is accessed in the #foreach
# directive. By default the reference $velocityCount will be available
# in the body of the #foreach directive. The default starting value
# for this reference is 1.
#----------------------------------------------------------------------------

directive.foreach.counter.name = velocityCount
directive.foreach.counter.initial.value = 1

#----------------------------------------------------------------------------
# I N C L U D E  P R O P E R T I E S
#----------------------------------------------------------------------------
# These are the properties that governed the way #include'd content
# is governed.
#----------------------------------------------------------------------------

directive.include.output.errormsg.start = <!-- include error :
directive.include.output.errormsg.end   =  see error log -->

#----------------------------------------------------------------------------
# P A R S E  P R O P E R T I E S
#----------------------------------------------------------------------------

directive.parse.max.depth = 10

#----------------------------------------------------------------------------
# T E M P L A T E  L O A D E R S
#----------------------------------------------------------------------------
#
#
#----------------------------------------------------------------------------

resource.loader = webapp

#webapp.resource.loader.class = org.apache.velocity.tools.view.servlet.WebappLoader
webapp.resource.loader.class = org.apache.velocity.tools.view.WebappResourceLoader
webapp.resource.loader.path = /
webapp.resource.loader.cache = true
webapp.resource.loader.modificationCheckInterval = 2
webapp.resource.loader.description = Velocity Webapp Resource Loader

#file.resource.loader.class = org.apache.velocity.runtime.resource.loader.FileResourceLoader
#file.resource.loader.path = another path
#file.resource.loader.cache = true
#file.resource.loader.modificationCheckInterval = 2
#file.resource.loader.description = Velocity File Resource Loader

#----------------------------------------------------------------------------
# VELOCIMACRO PROPERTIES
#----------------------------------------------------------------------------
# global : name of default global library.  It is expected to be in the regular
# template path.  You may remove it (either the file or this property) if
# you wish with no harm.
#----------------------------------------------------------------------------
#velocimacro.library = VM_global_library.vm

velocimacro.permissions.allow.inline = true
velocimacro.permissions.allow.inline.to.replace.global = false
velocimacro.permissions.allow.inline.local.scope = false

velocimacro.context.localscope = false

#----------------------------------------------------------------------------
# INTERPOLATION
#----------------------------------------------------------------------------
# turn off and on interpolation of references and directives in string
# literals.  ON by default :)
#----------------------------------------------------------------------------
runtime.interpolate.string.literals = true


#----------------------------------------------------------------------------
# RESOURCE MANAGEMENT
#----------------------------------------------------------------------------
# Allows alternative ResourceManager and ResourceCache implementations
# to be plugged in.
#----------------------------------------------------------------------------
resource.manager.class = org.apache.velocity.runtime.resource.ResourceManagerImpl
resource.manager.cache.class = org.apache.velocity.runtime.resource.ResourceCacheImpl

#----------------------------------------------------------------------------
# PLUGGABLE INTROSPECTOR
#----------------------------------------------------------------------------
# Allows alternative introspection and all that can of worms brings
#----------------------------------------------------------------------------

runtime.introspector.uberspect = org.apache.velocity.util.introspection.UberspectImpl