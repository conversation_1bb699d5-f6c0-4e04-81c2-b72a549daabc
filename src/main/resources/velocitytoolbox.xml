<?xml version="1.0"?>
<toolbox>
	<data type="string">
		<key>version</key>
		<value>2.0</value>
	</data>
	<tool>
		<key>math</key>
		<scope>application</scope>
		<class>org.apache.velocity.tools.generic.MathTool</class>
	</tool>
	<tool>
		<key>esc</key>
		<scope>application</scope>
		<class>org.apache.velocity.tools.generic.EscapeTool</class>
	</tool>
	<tool>
		<key>date</key>
		<scope>application</scope>
		<class>org.apache.velocity.tools.generic.DateTool</class>
		<parameter name="format" value="yyyy-MM-dd" />
	</tool>
	<tool>
		<key>datetime</key>
		<scope>application</scope>
		<class>org.apache.velocity.tools.generic.DateTool</class>
		<parameter name="format" value="yyyy-MM-dd HH:mm:ss" />
	</tool>
	<tool>
		<key>strUtil</key>
		<scope>application</scope>
		<class>ayou.util.StringUtil</class>
	</tool>
	<tool>
		<key>Util</key>
		<scope>application</scope>
		<class>com.cc.ovp.util.Util</class>
	</tool>
	<tool>
		<key>logicUtil</key>
		<scope>application</scope>
		<class>com.cc.ovp.util.LogicUtil</class>
	</tool>
	<tool>
		<key>Ext</key>
		<scope>application</scope>
		<class>com.cc.ovp.util.Ext</class>
	</tool>
	<tool>
		<key>IntHelp</key>
		<scope>application</scope>
		<class>com.cc.ovp.util.IntHelp</class>
	</tool>
	<tool>
		<key>Config</key>
		<scope>application</scope>
		<class>com.cc.ovp.util.Config</class>
	</tool>
	<tool>
		<key>IPMap</key>
		<scope>application</scope>
		<class>com.cc.ovp.util.IPMap</class>
	</tool>
		<tool>
		<key>getFileSize</key>
		<scope>application</scope>
		<class>ayou.util.FileUtil</class>
	</tool>
</toolbox>
