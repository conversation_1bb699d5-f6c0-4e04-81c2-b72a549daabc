<?xml version="1.0" encoding="UTF-8"?>
<!--
    logback.xml的基本配置信息都包含在configuration标签中，
    需要含有至少一个appender标签用于指定日志输出方式和输出格式，
    root标签为系统默认日志进程，通过level指定日志级别，
    通过appender-ref关联前面指定顶的日志输出方式。
 -->
<!-- 定义 每隔60秒中扫描该文件 -->
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <!--定义日志输出格式-->
    <property name="LOG_PATTERN" value="[%d][%p][%c:%L][%t] [requestId=%X{tractId}] %msg%n"/>
    <!--定义日志输出目录-->
    <property name="LOG_HOME" value="/data/logs/${PROJECT_NAME:-vpolyv}"/>
    <!--定义日志输出目录-->
    <property name="FILE_NAME" value="${PROJECT_NAME:-vpolyv}"/>
    <!--定义日志输出字符编码-->
    <property name="CHARSET" value="UTF-8"/>
    <!--maven profile-->
    <property name="LOG_ENV" value="${env}"/>

    <!-- 控制台输出的日志格式 -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
    </appender>

    <!-- 输出至统一日志文件中 -->
    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
        <file>${LOG_HOME}/${FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${FILE_NAME}-%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <!-- 日志文件保留天数 -->
            <maxHistory>180</maxHistory>
        </rollingPolicy>
    </appender>

    <root level="info">
        <appender-ref ref="RollingFile"/>
    </root>

</configuration>