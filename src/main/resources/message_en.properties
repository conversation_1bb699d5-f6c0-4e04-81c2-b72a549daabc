index.welcome=Welcome
index.myHome=Home
index.title=Polyv
index.backToHome=back
index.videoManage=Videos
index.adManage=Ads
index.Home=Home
index.stats=Stats
index.settings=Settings
index.setting=Settings
index.uploadutil=Uploader
index.exit=Exit
index.welcomemsg=Hello
index.package=account type
index.wanningMsg=The flow of the account is reaching out of quota
index.graph=statistical graph
index.spaceUsage=Space Usage
index.flowUsage=Flow Usage
index.leaveWanning=Please make sure everything saved,Are you sure you want to close the window?
index.spaceUsed=Used
index.spaceLeft=Available
index.flowUsed=Used
index.flowLeft=Available
index.dayleft=left
index.day=day
index.expire=Expiration Date

menu.main=Menu
menu.videos=Videos
menu.ads=Ads
menu.stats=Stats
menu.settings=Settings
menu.addAd=create Ads
menu.adlist=Ads list
menu.adsense=google adsense
menu.overview=overview
menu.space=space stats
menu.playstats=play stats
menu.flowstats=flow stats
menu.playduration=play duration
menu.adstats=Ads stats
menu.api=Developer
menu.editinfo=My Infomation
menu.editpass=Change Password
menu.accounts=Users
menu.center=data center

customertoken.code=Access key
customertoken.tip=provide access key to access video APIs
customertoken.sign=use signature when access API
customertoken.yes=yes
customertoken.no=no
customertoken.copy=copy
customertoken.change=change
customertoken.warn=changing access key will make orginal keys fail to access API, are you sure to proceed?
customertoken.uploader=embeddable uploader code
customertoken.appcode=ecrypted code for app sdk

menu_template.upload=Upload
menu_template.new=Videos List
menu_template.playersetting=Player Settings
menu_template.encryption=Video Settings
menu_template.playerlist=PlayList
menu_template.hostUrl=Domain Settings
menu_template.sourcedistribution=SourceFile Settings
menu_template.userinforecord=VisitorForm
menu_template.fileSync=VideoFileSync
menu_template.Trash=Trash
menu_template.cata=Category


upload.upload=Upload Videos
upload.filelist=Videos Files
upload.html5=Click Here to use a Resumable Upload Page.
upload.html5require=Require HTML5 support Browsers like Chrome,firefox

upload_template.filename=FileName
upload_template.filesize=Size
upload_template.type=Type
upload_template.action=Action
upload_template.tag=tag
upload_template.tagTip=mutilTags splited by comma
upload_template.cataselect=Category 
upload_template.luping=screen Capture Type
upload_template.videoFormat=Video Format:
upload_template.info=File Size limits:smaller than 2 gigabyte
upload_template.diskfull=Out of storage quota
upload_template.startButton=start_en.png
upload_template.selectButton=button_en.png

vnew.allvideos=Videos List
vnew.edit=Modify
vnew.delete=Delete
vnew.encodeAgain=EncodeAgain
vnew.captureImage=CaptureImage
vnew.keyPoint=KeyPoint
vnew.move=Move to
vnew.dump=Export
vnew.editCata=Edit Category
vnew.search=Search
vnew.encodeAgainTip=Are you sure to Encode this video again?
vnew.deleteTip=Are you sure to delete {num} videos?
vnew.captureImageTip=Are you sure to capture video's images?
vnew.warn=please select videos
vnew.moveDone=videos moved.
vnew.editorpick=editor pick
vnew.abnormal=abnormal videos


vnew_template.thumbnail=Thumbnail
vnew_template.title=Title
vnew_template.timesize=Date Modified
vnew_template.status=Status

vnew_list.thumbnail=Thumbnail
vnew_list.title=Title/Duration/vid
vnew_list.timesize=Date Modified/size
vnew_list.status=Status
vnew_list.owner=Uploader
vnew_list.category=Category
vnew_list.manager=admin

vnew_right.playtimes=play times
vnew_right.changeImage=change cover
vnew_right.downloadImage=download images
vnew_right.editorPick=editor pick
vnew_right.preview=preview
vnew_right.sourcefile=source file
vnew_right.subtitle=upload subtitle
vnew_right.promoteurl=promotion link
vnew_right.barcode=barcode
vnew_right.playsetting=play settings
vnew_right.playername=Player
vnew_right.defaultplayer=default player
vnew_right.playersize=player size
vnew_right.customsize=custom size
vnew_right.defaultdf=default quality
vnew_right.width=width
vnew_right.height=height
vnew_right.zero=0 to be self-adapting
vnew_right.autoplay=auto play
vnew_right.yes=yes
vnew_right.no=no
vnew_right.save=save
vnew_right.mutilplatform=multiplatform code
vnew_right.share=share
vnew_right.jstip=javascript code
vnew_right.copycode=copy code
vnew_right.htmltip=static HTML code(flash support only)
vnew_right.flashtip=Flash url
vnew_right.sharetip=share to sns web site
vnew_right.videoplay=Video resolution
vnew_right.childvideo=video pieces
vnew_right.add=add
vnew_right.wenda=examination questions
vnew_right.ppt=ppt/pdf
vnew_right.encryption=encryption settings in mobile devices
vnew_right.defaultsetting=default
vnew_right.open=open
vnew_right.web=web
vnew_right.app=app
vnew_right.desc=description
vnew_right.tag=tag
vnew_right.publishurl=publish url
vnew_right.visitorFormSettings=visitor Form Settings
vnew_right.open=open
vnew_right.close=close


status_-2=Deleted
status_-1=Deleted
status_5=Uploading
status_10=Waiting
status_20=Encoding
status_21=Encoding
status_22=Encoding
status_23=Encoding
status_24=Encoding
status_25=Encoding
status_26=Encoding
status_30=Encoding
status_40=EncodeFailed
status_41=EncodeFailed
status_42=EncodeFailed
status_43=Encoded
status_44=EncodeFailed
status_45=EncodeFailed
status_46=EncodeFailed
status_47=EncodeFailed
status_50=reviewing
status_51=rejected
status_52=reviewing
status_60=published
status_61=published


editpool.title=title
editpool.desc=description
editpool.tag=tag
editpool.publishurl=publish url
editpool.password=password
editpool.save=save
editpool.cancel=cancel

urlhostsetting.domainsetting=domain setting
urlhostsetting.numbertip=domains avaiable
urlhostsetting.nolimit=limitless
urlhostsetting.nolimittip=videos can be played in every domain website.
urlhostsetting.blacklist=blacklist
urlhostsetting.blacklisttip=videos can not be played in domains in this list
urlhostsetting.add=add
urlhostsetting.delete=delete
urlhostsetting.whitelist=whitelist
urlhostsetting.whitelisttip=videos can be played only in domains in this list
urlhostsetting.combination=combination
urlhostsetting.combinationtip=videos can be played in whitelist domains and can not be played in blacklist domains.
urlhostsetting.save=save
urlhostsetting.tip=
urlhostsetting.toomuch=domains num exceed quota
urlhostsetting.selecttip=select a radio button before add content
urlhostsetting.submitted=setting submitted
urlhostsetting.errorinfo=domain cannot be empty or domains num exceed quota



playermoresetting.playersetting=player setting
playermoresetting.max=you can add five players
playermoresetting.setdefault=set default
playermoresetting.add=add
playermoresetting.default=default player
playermoresetting.playername=player name
playermoresetting.playerid=player id
playermoresetting.action=action
playermoresetting.addtime=add time
playermoresetting.modify=modify
playermoresetting.delete=delete

playersetting.addplayer=add player
playersetting.property=property
playersetting.appearance=appearance
playersetting.skinsetting=skin setting
playersetting.piantousetting=titles setting
playersetting.playerlogo=logo
playersetting.watermark=water mark
playersetting.save=save

b1.panelcolor=panel color
b1.maincolor=main color
b1.matchcolor=match color
b1.transparency=transparency
b1.reset=reset

b2.uploadtitles=upload titles
b2.upload=upload
b2.current=current
b2.show=show
b2.hide=hide


b3.upload=upload
b3.current=current
b3.logolink=logo link
b3.upload=upload
b3.logotrans=transparency
b3.logoposition=logo position
b3.topleft=top left
b3.topright=top right
b3.leftbottom=left bottom
b3.rightbottom=right bottom
b3.hide=hide
b3.tip=keep logo image resolution less than 200x200

11.playersetting=player setting
11.playername=player name

6.sharesetting=share setting
6.share=sns share
6.codeshare=code copy share
6.download=download

7.menusetting=right menu setting
7.add=add
7.avaiable=avaiable

3.playlistsetting=playlist setting
3.top=top
3.left=left
3.right=right
3.bottom=bottom
3.inner=inner
3.imagetype=image type
3.texttype=text type

4.trailersetting=trailer setting
4.relatevideos=related videos
4.newvideos=newest videos
4.hotvideos=hot videos
4.pickedvideos=editor pick
4.novideos=empty
4.gohead=reset to start



5.rightmenu=right menu setting
5.showlight=show lights button
5.showshare=show share button
5.showdf=show quality button
5.widescreen=show screen proportion button

8.languagesetting=player language
8.cn=Chinese
8.en=English
8.prefer=According to browser

playersettingupdate.update=modify player

encryption.setting=video setting
encryption.encryptionsetting=encryption setting
encryption.tip=only affect new videos after encryption setting changes
encryption.mobilesetting=encryption settings in mobile devices
encryption.open=open
encryption.web=web
encryption.app=app
encryption.watermark=watermark
encryption.barcode=barcode page
encryption.barcodeopen=open
encryption.barcodeclose=close
encryption.save=save

111.level1=360P
111.level2=480P
111.level3=720P
111.tip=only affect new videos after setting changes


b44.watermark=upload watermark
b44.wmposition=watermark position
b44.topleft=top left
b44.topright=top right
b44.leftbottom=left bottom
b44.rightbottom=right bottom
b44.hide=hide
b44.tip=

sourcedistribution.setting=source file download setting
sourcedistribution.tip=check this to permit source file downloading

news.playlist=playlist
news.add=add
news.delete=delete
news.search=search
news.image=thumbnail
news.title=title
news.author=author
news.lastmodified=lastmodified
news.action=action
news.edit=edit
news.code=code
news.preview=preview

userInfoRecord.setting=visitor info form setting
userInfoRecord.name=name
userInfoRecord.presenttime=present time
userInfoRecord.viewdata=view data
userInfoRecord.jumpurl=image click link
userInfoRecord.imageurl=image url
userInfoRecord.upload=upload image
userInfoRecord.current=current image
userInfoRecord.field=field in form
userInfoRecord.add=add
userInfoRecord.save=save

recycle.list=videos
recycle.recover=recover
recycle.delete=delete
recycle.recoverconfirm=Are you sure to put {num} videos back?
recycle.deleteconfirm=Are you sure to delete {num} videos?
recycle.search=search

customer-edit.weburlnull=web url field can't be empty
customer-edit.modify=modify
customer-edit.person=personal information
customer-edit.name=name
customer-edit.company=company name
customer-edit.telephone=mobile phone
customer-edit.officetel=office telephone
customer-edit.websiteinfo=website information
customer-edit.weburl=web url
customer-edit.webname=website name
customer-edit.province=province
customer-edit.city=city
customer-edit.other=other settings
customer-edit.callbackurl=callback url
customer-edit.autoedit=auto update title
customer-edit.autoedittip=auto update title,publish url from referer page's

edit-password.passconfirm=password cannot be empty
edit-password.passconfirmagain=password cannot be empty
edit-password.passconfirmlength=password must be more than 6 characters
edit-password.passconfirmmatch=two passwords don't match
edit-password.modify=modify password
edit-password.oldpassword=old password
edit-password.newpassword=new password
edit-password.newpassword2=new password again

childUserManager.add=add new account
childUserManager.max=You can only add up to 15 sub-accounts
childUserManager.name=name
childUserManager.type=type
childUserManager.operator=operator
childUserManager.action=action
childUserManager.expirationtime=expiration time
childUserManager.mainaccount=main account
childUserManager.editor=editor
childUserManager.admin=admin

childUserAdd.pass=password
childUserAdd.name=name
childUserAdd.type=type
childUserAdd.choose=select a type
childUserAdd.editor=editor
childUserAdd.operator=operator
childUserAdd.admin=admin
childUserAdd.expiretime=expiration
childUserAdd.success=done
childUserAdd.exits=email exits

adv-setting.createAd=create Ad
adv-setting.adname=Ad name
adv-setting.addesc=description
adv-setting.matter=Ad file
adv-setting.mattertip=JPEG,GIF,PNG,FLV,MP4,SWF,WMV,MOV,3GP,AVI.
adv-setting.adlink=Ad link
adv-setting.adtype=Ad type
adv-setting.adpiantou=titles
adv-setting.adpause=pause
adv-setting.adtrailer=trailer
adv-setting.duration=duration
adv-setting.startdate=start date
adv-setting.enddate=end date
adv-setting.starttime=start time
adv-setting.endtime=end time
adv-setting.status=status
adv-setting.online=online
adv-setting.ready=ready
adv-setting.offline=offline
adv-setting.deleted=deleted
adv-setting.floattype=floated
adv-setting.floattip=only for pause type
adv-setting.adroll=roll



yes=yes
no=no
width=width
height=height
save=save
startdate=start date
enddate=end date



ads-list.adname=Ad name
ads-list.addesc=description
ads-list.playtime=play times
ads-list.clicktime=click times
ads-list.preview=preview
ads-list.delete=delete
ads-list.modify=modify


ads-tab.head=Ad list


adgoogleedit.setting=adsense setting
adgoogleedit.disabled=disabled
adgoogleedit.account=account

overview.head=play stats overview(by month)
overview.month=month
overview.flow=flow
overview.duration=duration
overview.ip=ip
overview.num=play times
overview.detail=detail

space-list.storage=storage
space-list.date=date
space-list.logintimes=login times
space-list.uploadtimes=upload times

flow-list.head=play stats
flow-list.date=date
flow-list.visitors=visitors
flow-list.playtimes=pc flow/mobile flow
flow-list.ip=ip
flow-list.duration=duration
flow-list.detail=detail

sinflow-list.head=flow stats

duration-list.head=duration stats
my-list.head=Ad stats
my-list.name=name
my-list.status=status
my-list.playtimes=play times
my-list.clicktimes=click times

















