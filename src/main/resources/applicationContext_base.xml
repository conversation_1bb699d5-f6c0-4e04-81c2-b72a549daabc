<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:p="http://www.springframework.org/schema/p"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:tx="http://www.springframework.org/schema/tx"
	   xmlns:aop="http://www.springframework.org/schema/aop"
	   xmlns:task="http://www.springframework.org/schema/task"
	   xmlns:apollo="http://www.ctrip.com/schema/apollo"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
	    http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd">

	<apollo:config/>
	<apollo:config namespaces="backend.audit-common,backend.vod-videojson,backend.vod-common,backend.vod-datasource,backend.aliyun,backend.huawei-obs,backend.object-storage" />

	<aop:aspectj-autoproxy expose-proxy="true" proxy-target-class="true"/>
	<task:annotation-driven executor="defaultExecutor" />
	<!-- 配置支持注解方式声明事务 -->
	<tx:annotation-driven transaction-manager="transactionManager"/>
	<task:executor id="defaultExecutor" pool-size="0-80" keep-alive="3600" />

	<bean id="wallConfig" class="com.alibaba.druid.wall.WallConfig" >
		<property name="deleteWhereAlwayTrueCheck" value="true" />
		<property name="deleteWhereNoneCheck" value="true" />
		<property name="updateWhereAlayTrueCheck" value="true" />
		<property name="updateWhereNoneCheck" value="true" />
	</bean>
	<bean id="wallFilter" class="com.alibaba.druid.wall.WallFilter">
		<property name="dbType" value="mysql" />
		<property name="throwException" value="false" />
		<property name="logViolation" value="true" />
		<property name="config" ref="wallConfig" />
	</bean>
	<bean id="slf4jFilter" class="com.alibaba.druid.filter.logging.Slf4jLogFilter" />

	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close">
		<property name="url" value="${vod.mysql.primary.url}" />
		<property name="username" value="${vod.mysql.primary.username}" />
		<property name="password" value="${vod.mysql.primary.password}" />
		<property name="driverClassName" value="com.mysql.jdbc.Driver" />

		<property name="maxActive" value="100" />
		<property name="initialSize" value="5" />
		<property name="maxWait" value="60000" />
		<property name="minIdle" value="2" />
		<property name="poolPreparedStatements" value="false" />
		
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="300000" />

		<property name="validationQuery" value="SELECT '1'" />
		<property name="testWhileIdle" value="true" />
		<property name="testOnBorrow" value="true" />
		<property name="testOnReturn" value="false" />
		<property name="maxOpenPreparedStatements"
			value="20" />
		<property name="removeAbandoned" value="true" /> <!-- 打开removeAbandoned功能 -->
	    <property name="removeAbandonedTimeout" value="600" /> <!-- 1800秒，也就是30分钟 -->
	    <property name="logAbandoned" value="true" /> <!-- 关闭abanded连接时输出错误日志 -->
		<property name="proxyFilters">
			<list>
				<ref bean="wallFilter" />
				<ref bean="slf4jFilter" />
			</list>
		</property>
	</bean>
	
	<bean id="liveDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close">
		<property name="url" value="${vod.mysql.live.url}" />
		<property name="username" value="${vod.mysql.live.username}" />
		<property name="password" value="${vod.mysql.live.password}" />
		<property name="driverClassName" value="com.mysql.jdbc.Driver" />

		<property name="maxActive" value="100" />
		<property name="initialSize" value="5" />
		<property name="maxWait" value="60000" />
		<property name="minIdle" value="10" />
		<!-- <property name="poolPreparedStatements" value="false" />  -->
		
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="300000" />

		<property name="validationQuery" value="SELECT '1'" />
		<property name="testWhileIdle" value="true" />
		<property name="testOnBorrow" value="true" />
		<property name="testOnReturn" value="false" />
		<property name="maxOpenPreparedStatements"
			value="20" />
		<property name="removeAbandoned" value="true" /> <!-- 打开removeAbandoned功能 -->
	    <property name="removeAbandonedTimeout" value="1800" /> <!-- 1800秒，也就是30分钟 -->
	    <property name="logAbandoned" value="true" /> <!-- 关闭abanded连接时输出错误日志 -->
		<property name="proxyFilters">
			<list>
				<ref bean="wallFilter" />
				<ref bean="slf4jFilter" />
			</list>
		</property>
	</bean>
	
	<!-- 事务管理器 -->
	<bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource"/>
		<!-- 
		<property name="nestedTransactionAllowed" value="true"/>
		<property name="rollbackOnCommitFailure" value="true"/>
		<property name="validateExistingTransaction" value="true"/>
		 -->
	</bean>
	<!-- 事务模板 -->
	<bean id="transactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">  
		<property name="transactionManager" ref="transactionManager" /> 
		<property name="timeout" value="10"></property>
		<property name="isolationLevelName" value="ISOLATION_READ_COMMITTED"></property>
	</bean> 
	
	<!-- ibatis sqlmapclient -->
	<bean id="sqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
		<property name="configLocation" value="classpath:sqlMapConfig.xml"/>
		<property name="dataSource" ref="dataSource" />
		<property name="useTransactionAwareDataSource" value="true"/>
	</bean>

	
	<!-- sqlmapclient 模板 -->
	<bean id="sqlMapClientTemplate" class="org.springframework.orm.ibatis.SqlMapClientTemplate">
		<property name="sqlMapClient" ref="sqlMapClient"/>
	</bean>

	<!-- 发送邮件 -->
	<bean id="mailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="protocol" value="smtps" />
        <property name="host" value="c2.icoremail.net" />
<!--        <property name="port" value="25" />-->
        <property name="username" value="<EMAIL>" />
        <property name="password" value="DNkZz4uyswJTxFVD" />
		<property name="defaultEncoding" value="UTF-8" />
        <property name="javaMailProperties">  
            <props>  
                <prop key="mail.smtps.auth">true</prop>
                <prop key="mail.smtps.timeout">25000</prop>
                <!-- <prop key="mail.smtp.starttls.enable">true</prop>
                <prop key="mail.smtp.socketFactory.class">javax.net.ssl.SSLSocketFactory</prop>
                <prop key="mail.smtp.socketFactory.fallback">false</prop>  -->
            </props>  
        </property>  

  </bean>
	

	<bean id="videoJson" class="com.cc.ovp.web.video.VideoJson"/>
	<bean id="userJson" class="com.cc.ovp.web.video.UserJson"/>
	<bean id="videoXML" class="com.cc.ovp.web.video.VideoXML"/>
	
	

	<bean id="healthService" class="net.polyv.commons.health.service.HealthService"
		  factory-method="getInstance">
		<constructor-arg value="${internal-health-token}"/>
	</bean>

	<bean id="aliOssService" class="net.polyv.oss.ali.service.AliOssService"
		  factory-method="getInstanceByMode" destroy-method="shutdown">
		<constructor-arg type="com.ctrip.framework.apollo.Config" ref="aliyunConfig" />
		<constructor-arg type="net.polyv.oss.ali.enumeration.AliOssConnectMode" value="INTERNAL"/>
	</bean>

	<bean id="aliyunConfig" class="com.ctrip.framework.apollo.ConfigService" factory-method="getConfig" >
		<constructor-arg value="backend.aliyun"/>
	</bean>

	<bean id="huaweiObsConfig" class="com.ctrip.framework.apollo.ConfigService" factory-method="getConfig" >
		<constructor-arg value="backend.huawei-obs"/>
	</bean>

	<bean id="obsService" class="net.polyv.huawei.obs.service.OBSService"
		  factory-method="getInstanceByMode" destroy-method="shutdown">
		<constructor-arg type="com.ctrip.framework.apollo.Config" ref="huaweiObsConfig" />
	</bean>

	<!-- 腾讯云对象存储配置 -->
	<bean id="txObjectStorageConfig" class="com.ctrip.framework.apollo.ConfigService" factory-method="getConfig" >
		<constructor-arg value="backend.object-storage"/>
	</bean>

	<bean id="cosConfigProperties" class="net.polyv.service.objectstorage.tx.COSConfigProperties">
		<constructor-arg type="com.ctrip.framework.apollo.Config" ref="txObjectStorageConfig" />
	</bean>

<!--	<bean id="txObjectService" class="net.polyv.commons.object.impl.tx.TXObjectService"/>-->

	<bean id="jedisPool" class="com.cc.ovp.util.RedisUtils" factory-method="getPool"></bean>

	<!-- 目前只有国网账号 (df3221ee0f) 会使用特殊配置，其他账号获取到的都是“空配置”（各项配置的值都为null） -->
	<bean id="cdnConfigRepository" class="net.polyv.vod.config.cdn.CdnConfigSguRepository">
	</bean>
	<bean id="specialCDNConfig" class="net.polyv.vod.config.SpecialCDNConfig">
		<constructor-arg name="cdnConfigRepository" ref="cdnConfigRepository"></constructor-arg>
	</bean>

	<!-- <import resource="classpath:mq/applicationContext_activemq.xml"/>  -->
	
	<import resource="classpath:applicationContext_mongo.xml"/> 
	<!--<import resource="classpath:applicationContext_rmiService.xml"/>-->

	<bean class="com.cc.ovp.OVPContext"/>

	<bean id="initRedis2Utils" class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="targetClass" value="com.cc.ovp.util.Redis2Utils"/>
		<property name="targetMethod" value="init"/>
		<property name="arguments">
			<list>
				<value>${vod.redis.v2.host}</value>
				<value>${vod.redis.v2.port}</value>
			</list>
		</property>
	</bean>

	<bean id="initRedisUtils" class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
		<property name="targetClass" value="com.cc.ovp.util.RedisUtils"/>
		<property name="targetMethod" value="init"/>
		<property name="arguments">
			<list>
				<value>${vod.redis.host}</value>
				<value>${vod.redis.port}</value>
			</list>
		</property>
	</bean>

	<bean class="net.polyv.vod.service.BWService">
		<constructor-arg ref="apolloConfigService" />
	</bean>

	<!-- 配置自动扫描组件 -->
	<context:component-scan base-package="com.cc.ovp.dao"/>
	<context:component-scan base-package="com.cc.ovp.manager"/>
	<context:component-scan base-package="com.cc.ovp.service"/>

	<!-- 代理平台 -->
		<context:component-scan base-package="com.cc.AgentPlatform.dao"/>
	<context:component-scan base-package="com.cc.AgentPlatform.service"/>
	
	<!-- 用户中心 -->
	<context:component-scan base-package="com.cc.usercenter.dao"/>
	<context:component-scan base-package="com.cc.usercenter.service"/>
	
	<context:component-scan base-package="net.polyv.service"/>
	<context:component-scan base-package="net.polyv.web.service"/>

</beans>
