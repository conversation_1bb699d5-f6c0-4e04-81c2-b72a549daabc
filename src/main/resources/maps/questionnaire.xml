<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="question">
    <typeAlias alias="questionnaire" type="com.cc.ovp.domain.Questionnaire" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

  <sql id="_columns">
		qid, userid, title, time, link, imagelink, ext, status
  </sql>

	<insert id="insert" parameterClass="questionnaire">
	<![CDATA[
		insert into questionnaire
		(qid, userid, title, time, link, imagelink, ext, status, autokey)
		values
		(#qid#, #userid#, #title#, #time#, #link#, #imagelink#, #ext#, #status#, #autokey#)
     ]]>
	</insert>
	
	<select id="selectInfo" parameterClass="questionnaire" resultClass="questionnaire">
	    select * from questionnaire where userid=#userid#
	</select>
	
	<select id="selectInfoByuserid" parameterClass="java.lang.String" resultClass="questionnaire">
	    select * from questionnaire where userid=#userid#
	</select>
	
	<select id="selectInfoByqid" parameterClass="java.lang.String" resultClass="questionnaire">
	    select * from questionnaire where qid=#qid#
	</select>
	
	<update id="updateInfo" parameterClass="questionnaire">
	     update questionnaire set  title=#title#, time=#time#, link=#link#, imagelink=#imagelink#, ext=#ext#, status=#status#  where userid=#userid#
	</update>
	
	<update id="updateAutokey" parameterClass="questionnaire">
	     update questionnaire set  autokey=#autokey#  where qid=#qid#
	</update>
	    
</sqlMap>