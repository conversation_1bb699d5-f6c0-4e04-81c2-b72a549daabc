<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="ip_stat">
	<typeAlias alias="ipstat" type="com.cc.ovp.domain.IPStat" />
	<!-- 查询是否已经有记录 -->
	<select id="check" parameterClass="ipstat" resultClass="ipstat">
		select * from ip_stat where ip = #ip# and indate =#indate# and userid=#userid#
	</select>
	<!-- 查询某个ip在某天共看了多少个用户的视频 -->
	<select id="countByIP" parameterClass="String" resultClass="int">
		select count(*) from ip_stat where ip = #ip# 
	</select>
	<select id="countIPbyUserid" parameterClass="java.util.Map" resultClass="int">
		select count(*) from ip_stat where userid = #userid# and indate=#indate#
	</select>
	
	<!-- 第一次,创建访问ip记录 -->
	<insert id="insert" parameterClass="ipstat">
		insert into ip_stat (ip,indate,userid,times,province,city,refer,country) 
		values (#ip#,#indate#,#userid#,#times#,#province#,#city#,#refer#,#country#)
	</insert>

	<!-- 更新记录 -->
	<update id="update" parameterClass="com.cc.ovp.domain.IPStat">
		update ip_stat set times=times+1 where ip = #ip# and indate =#indate# and userid=#userid#
	</update>
	<!-- 查看某个时间段的ip数 -->
	<select id="selectCountByDate" parameterClass="java.util.Map" resultClass="int">
	<![CDATA[select count(userid) from ip_stat
		    where datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0 and userid=#userid# ]]>
	</select>
	
	<select id="selectListByDate" parameterClass="java.util.Map" resultClass="ipstat">
		<![CDATA[select *  from ip_stat 
		where datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0 and userid=#userid#]]>
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
		
	</select>
	
</sqlMap>
