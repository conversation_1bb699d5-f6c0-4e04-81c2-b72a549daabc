<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="ccUserChildren">
    <typeAlias alias="userChildren" type="com.cc.usercenter.domain.ChildrenUser" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

  <sql id="_columns">
		childuserid, childusername, userid, childemail, password, userlevel, expiredate
  </sql>

	<insert id="insert" parameterClass="userChildren">
	<![CDATA[
		insert into cc_user_children
		(childuserid, childusername, userid, childemail, password, userlevel, expiredate, userEmail)
		values
		(#childuserid#, #childusername#, #userid#, #childemail#, #password#, #userlevel#, #expiredate#,#useremail#)
     ]]>
	</insert>
	
	<select id="selectAllByUserid" parameterClass="java.lang.String" resultClass="userChildren">
	    select * from cc_user_children where userid=#userid#
	</select>
	
	<select id="selectOneByChilduserid" parameterClass="java.lang.String" resultClass="userChildren">
	    select * from cc_user_children where childuserid=#childuserid#
	</select>
	
	<update id="updateBychilduserid" parameterClass="userChildren">
	     update cc_user_children set  childusername=#childusername#, childemail=#childemail#, password=#password#, userlevel=#userlevel#, expiredate=#expiredate#  where childuserid=#childuserid#
	</update>
	
	<update id="updateUserEmailByUserId" parameterClass="java.util.Map">
		update cc_user_children set userEmail=#userEmail# where userid=#userId#
	</update>
	
	<delete id="deleteBychilduserid" parameterClass="java.lang.String">
	    delete from cc_user_children where childuserid=#childuserid#
	</delete>
	
	<select id="selectBychildemail" parameterClass="java.lang.String" resultClass="userChildren">
	    select * from cc_user_children where childemail=#childemail#
	</select>

    
</sqlMap>