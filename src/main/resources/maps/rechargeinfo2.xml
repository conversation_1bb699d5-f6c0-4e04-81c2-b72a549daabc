<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="rechargeinfo2">

	<typeAlias alias="rechargeinfo2" type="com.cc.AgentPlatform.domain.Rechargeinfo2" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							id,
							orderid,
							currency,
							status,
							cmoney,
							description,
							userid,
							payment,
							datetime,
							flow,
							available_traffic_id,
							del
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="orderid"> 
									<isNotEmpty prepend="AND" property="orderid"> 
										<![CDATA[ orderid = #orderid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="currency"> 
									<isNotEmpty prepend="AND" property="currency"> 
										<![CDATA[ currency = #currency# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="status"> 
									<isNotEmpty prepend="AND" property="status"> 
										<![CDATA[ status = #status# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="cmoney"> 
									<isNotEmpty prepend="AND" property="cmoney"> 
										<![CDATA[ cmoney = #cmoney# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="description"> 
									<isNotEmpty prepend="AND" property="description"> 
										<![CDATA[ description = #description# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="payment"> 
									<isNotEmpty prepend="AND" property="payment"> 
										<![CDATA[ payment = #payment# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="datetime"> 
									<isNotEmpty prepend="AND" property="datetime"> 
										<![CDATA[datetime<DATE_FORMAT( '$datetime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="flow"> 
									<isNotEmpty prepend="AND" property="flow"> 
										<![CDATA[ flow = #flow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="available_traffic_id"> 
									<isNotEmpty prepend="AND" property="available_traffic_id"> 
										<![CDATA[ available_traffic_id = #available_traffic_id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="rechargeinfo2">
		insert into rechargeinfo2 
		(
							id,
							orderid,
							currency,
							status,
							cmoney,
							description,
							userid,
							payment,
							datetime,
							flow,
							available_traffic_id,
							del
		)
		values
		(
							#id#,
							#orderid#,
							#currency#,
							#status#,
							#cmoney#,
							#description#,
							#userid#,
							#payment#,
							#datetime#,
							#flow#,
							#available_traffic_id#,
							#del#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from rechargeinfo2 where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="rechargeinfo2">
		select <include refid="_columns"/> from rechargeinfo2 where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="rechargeinfo2">
		select <include refid="_columns"/> from rechargeinfo2 where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="rechargeinfo2">
		select <include refid="_columns"/> from rechargeinfo2 where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from rechargeinfo2 where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="rechargeinfo2">
		select <include refid="_columns"/> from rechargeinfo2
	</select>
	
		<update id="update" parameterClass="rechargeinfo2">
		update rechargeinfo2 set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="orderid"> 
							<isNotEmpty property="orderid"> 
								orderid = #orderid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="currency"> 
							<isNotEmpty property="currency"> 
								currency = #currency# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="status"> 
							<isNotEmpty property="status"> 
								status = #status# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="cmoney"> 
							<isNotEmpty property="cmoney"> 
								cmoney = #cmoney# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="description"> 
							<isNotEmpty property="description"> 
								description = #description# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="payment"> 
							<isNotEmpty property="payment"> 
								payment = #payment# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="datetime"> 
							<isNotEmpty property="datetime"> 
								datetime = #datetime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="flow"> 
							<isNotEmpty property="flow"> 
								flow = #flow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="available_traffic_id"> 
							<isNotEmpty property="available_traffic_id"> 
								available_traffic_id = #available_traffic_id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

