<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="packagetype">

	<typeAlias alias="packagetype" type="com.cc.AgentPlatform.domain.Packagetype" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							id,
							name,
							enName,
							flow,
							price,
							txt,
							del,
							addtime,
							validity,
							space,
							addvalidity,
							datetype,
							munber
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="addvalidity"> 
									<isNotEmpty prepend="AND" property="addvalidity"> 
										<![CDATA[ addvalidity = #addvalidity# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="space"> 
									<isNotEmpty prepend="AND" property="space"> 
										<![CDATA[ space = #space# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
						
								<isPropertyAvailable property="name"> 
									<isNotEmpty prepend="AND" property="name"> 
										<![CDATA[ name = #name# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

								<isPropertyAvailable property="enName"> 
									<isNotEmpty prepend="AND" property="enName"> 
										<![CDATA[ enName = #enName# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
						
								<isPropertyAvailable property="flow"> 
									<isNotEmpty prepend="AND" property="flow"> 
										<![CDATA[ flow = #flow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="price"> 
									<isNotEmpty prepend="AND" property="price"> 
										<![CDATA[ price = #price# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="txt"> 
									<isNotEmpty prepend="AND" property="txt"> 
										<![CDATA[ txt = #txt# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="addtime"> 
									<isNotEmpty prepend="AND" property="addtime"> 
										<![CDATA[addtime<DATE_FORMAT( '$addtime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="validity"> 
									<isNotEmpty prepend="AND" property="validity"> 
										<![CDATA[validity<DATE_FORMAT( '$validity$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="packagetype">
		insert into packagetype 
		(
							id,
							name,
							enName,
							flow,
							price,
							txt,
							del,
							addtime,
							validity,
							space,
							addvalidity,
							datetype,
							munber
		)
		values
		(
							#id#,
							#name#,
							#enName#,
							#flow#,
							#price#,
							#txt#,
							#del#,
							#addtime#,
							#validity#,
							#space#,
							#addvalidity#,
							#datetype#,
							#munber#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from packagetype where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="packagetype">
		select <include refid="_columns"/> from packagetype where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="packagetype">
		select <include refid="_columns"/> from packagetype where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="packagetype">
		select <include refid="_columns"/> from packagetype where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from packagetype where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="packagetype">
		select <include refid="_columns"/> from packagetype
	</select>
	
		<update id="update" parameterClass="packagetype">
		update packagetype set
						<isPropertyAvailable property="addvalidity"> 
							<isNotEmpty property="addvalidity"> 
								addvalidity = #addvalidity# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="space"> 
							<isNotEmpty property="space"> 
								space = #space# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="name"> 
							<isNotEmpty property="name"> 
								name = #name# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="enName"> 
							<isNotEmpty property="enName"> 
								enName = #enName# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="flow"> 
							<isNotEmpty property="flow"> 
								flow = #flow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="price"> 
							<isNotEmpty property="price"> 
								price = #price# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="txt"> 
							<isNotEmpty property="txt"> 
								txt = #txt# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="addtime"> 
							<isNotEmpty property="addtime"> 
								addtime = #addtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="validity"> 
							<isNotEmpty property="validity"> 
								validity = #validity# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="addvalidity"> 
							<isNotEmpty property="addvalidity"> 
								addvalidity = #addvalidity# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="datetype"> 
							<isNotEmpty property="datetype"> 
								datetype = #datetype# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="munber"> 
							<isNotEmpty property="munber"> 
								munber = #munber# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

