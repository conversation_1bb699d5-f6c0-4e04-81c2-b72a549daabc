<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="orders">

	<typeAlias alias="orders" type="com.cc.AgentPlatform.domain.Orders" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							orderid,
							currency,
							status,
							cmoney,
							description,
							userid,
							payment,
							del,
							datetime,
							flow,
							id,
							space,
							type,
							packagetypeid,
							validity,
							auserid,
							ext
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="orderid"> 
									<isNotEmpty prepend="AND" property="orderid"> 
										<![CDATA[ orderid = #orderid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="auserid"> 
									<isNotEmpty prepend="AND" property="auserid"> 
										<![CDATA[ auserid = #auserid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="ext"> 
									<isNotEmpty prepend="AND" property="ext"> 
										<![CDATA[ ext = #ext# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="space"> 
									<isNotEmpty prepend="AND" property="space"> 
										<![CDATA[ space = #space# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
						
								<isPropertyAvailable property="currency"> 
									<isNotEmpty prepend="AND" property="currency"> 
										<![CDATA[ currency = #currency# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="status"> 
									<isNotEmpty prepend="AND" property="status"> 
										<![CDATA[ status = #status# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="cmoney"> 
									<isNotEmpty prepend="AND" property="cmoney"> 
										<![CDATA[ cmoney = #cmoney# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="description"> 
									<isNotEmpty prepend="AND" property="description"> 
										<![CDATA[ description = #description# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="payment"> 
									<isNotEmpty prepend="AND" property="payment"> 
										<![CDATA[ payment = #payment# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="datetime"> 
									<isNotEmpty prepend="AND" property="datetime"> 
										<![CDATA[datetime<DATE_FORMAT( '$datetime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>						
								<isPropertyAvailable property="flow"> 
									<isNotEmpty prepend="AND" property="flow"> 
										<![CDATA[ flow = #flow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	<!--  -->
	<insert id="insert" parameterClass="orders">
		insert into orders 
		(
							orderid,
							currency,
							status,
							cmoney,
							description,
							userid,
							payment,
							del,
							datetime,
							flow,
							id,
							space,
							type,
							packagetypeid,
							validity,
							auserid,
							ext
		)
		values
		(
							#orderid#,
							#currency#,
							#status#,
							#cmoney#,
							#description#,
							#userid#,
							#payment#,
							#del#,
							#datetime#,
							#flow#,
							#id#,
							#space#,
							#type#,
							#packagetypeid#,
							#validity#,
							#auserid#,
							#ext#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from orders where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="orders">
		select <include refid="_columns"/> from orders where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="orders">
		select <include refid="_columns"/> from orders where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="orders">
		select <include refid="_columns"/> from orders where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from orders where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="orders">
		select <include refid="_columns"/> from orders
	</select>
	
		<update id="update" parameterClass="orders">
		update orders set
						<isPropertyAvailable property="auserid"> 
							<isNotEmpty property="auserid"> 
								auserid = #auserid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="ext"> 
							<isNotEmpty property="ext"> 
								ext = #ext# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="space"> 
							<isNotEmpty property="space"> 
								space = #space# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="orderid"> 
							<isNotEmpty property="orderid"> 
								orderid = #orderid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="currency"> 
							<isNotEmpty property="currency"> 
								currency = #currency# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="status"> 
							<isNotEmpty property="status"> 
								status = #status# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="cmoney"> 
							<isNotEmpty property="cmoney"> 
								cmoney = #cmoney# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="description"> 
							<isNotEmpty property="description"> 
								description = #description# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="payment"> 
							<isNotEmpty property="payment"> 
								payment = #payment# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="datetime"> 
							<isNotEmpty property="datetime"> 
								datetime = #datetime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="flow"> 
							<isNotEmpty property="flow"> 
								flow = #flow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="type"> 
							<isNotEmpty property="type"> 
								type = #type# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="packagetypeid"> 
							<isNotEmpty property="packagetypeid"> 
								packagetypeid = #packagetypeid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="validity"> 
							<isNotEmpty property="validity"> 
								validity = #validity# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>

</sqlMap>

