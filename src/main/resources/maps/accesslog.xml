<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="accesslog">
	<typeAlias alias="accesslog" type="com.cc.ovp.domain.AccessLog" />
	<sql id="_columns">
		userid, indate, flowsize,refer,videoid,ip,starttime,endtime,date,stat_duration,browser,pid
	</sql>
	
	
		 
    <resultMap id="AccessResultMap" class="com.cc.ovp.domain.AccessLog" >
    <result column="userid" property="userid" jdbcType="VARCHAR" />
    <result column="indate" property="indate" jdbcType="DATE" />
     <result column="date" property="date" jdbcType="DATETIME" />
    <result column="videoid" property="videoid" jdbcType="VARCHAR" />
    <result column="starttime" property="starttime" jdbcType="BIGINT" />
    <result column="endtime" property="endtime" jdbcType="BIGINT" />
    <result column="refer" property="refer" jdbcType="VARCHAR" />
    <result column="browser" property="browser" jdbcType="VARCHAR" />
    <result column="flowsize" property="flowsize" jdbcType="BIGINT" />
    <result column="ip" property="ip" jdbcType="VARCHAR" />
    <result column="pid" property="pid" jdbcType="BIGINT" />
    <result column="stat_duration" property="stat_duration" jdbcType="BIGINT" />

  </resultMap>
	
		<select id="selectList" parameterClass="java.util.Map" resultMap="AccessResultMap">
		select <include refid="_columns"/>   from accesslog where 1=1
		  <isPropertyAvailable property="videoid"> 
									<isNotEmpty prepend="AND" property="videoid"> 
										videoid = #videoid#
									</isNotEmpty>
		</isPropertyAvailable>
		  <isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										userid = #userid#
									</isNotEmpty>
		</isPropertyAvailable>
		  <isPropertyAvailable property="indate"> 
									<isNotEmpty prepend="AND" property="indate"> 
										indate = #indate#
									</isNotEmpty>
		</isPropertyAvailable>
		<isPropertyAvailable property="startDate"> 
									<isNotEmpty prepend="AND" property="startDate"> 
										<![CDATA[datediff(indate,#startDate#)>=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		
		
		<isPropertyAvailable property="endDate"> 
									<isNotEmpty prepend="AND" property="endDate"> 
										<![CDATA[datediff(indate,#endDate#)<=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		 
		
		
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount"  parameterClass="java.util.Map" resultClass="long">
		select count(userid) from accesslog  where 1=1
		  <isPropertyAvailable property="videoid"> 
									<isNotEmpty prepend="AND" property="videoid"> 
										videoid = #videoid#
									</isNotEmpty>
		</isPropertyAvailable>
		
		  <isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										userid = #userid#
									</isNotEmpty>
		</isPropertyAvailable>
		  <isPropertyAvailable property="indate"> 
									<isNotEmpty prepend="AND" property="indate"> 
										indate = #indate#
									</isNotEmpty>
		</isPropertyAvailable>
		<isPropertyAvailable property="startDate"> 
									<isNotEmpty prepend="AND" property="startDate"> 
										<![CDATA[datediff(indate,#startDate#)>=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		
		
		<isPropertyAvailable property="endDate"> 
									<isNotEmpty prepend="AND" property="endDate"> 
										<![CDATA[datediff(indate,#endDate#)<=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		 
	</select>
	<select id="selectNewList"  resultMap="AccessResultMap">
		select * from accesslog order by indate desc limit 100
	</select>
	
	
</sqlMap>