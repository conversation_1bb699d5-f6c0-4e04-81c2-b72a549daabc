<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="param">
   <typeAlias alias="row" type="com.cc.ovp.util.Row" />
   <typeAlias alias="DefinedParamType" type="com.cc.ovp.domain.DefinedParamType" />
   <typeAlias alias="DefinedParam" type="com.cc.ovp.domain.DefinedParam" />
  <update id="updateParamTypeStatus" parameterClass="row">
  	update param_type set status = #status#,updatetime=NOW() where id = #id#;
  </update>
  <update id="updateParamStatus" parameterClass="row">
  	update param set status = #status#,updatetime=NOW() where id = #id#;
  </update>
  <insert id="addParamType"  parameterClass="DefinedParamType">
    INSERT INTO param_type (id,indexid,typename,descri,db_id,pic_id,article,posttime,updatetime,userid,status,parentid) 
       values (#id#,#indexid#,#typename#,#descri#,#db_id#,#pic_id#,#article#,now(),now(),#userid#,#status#,#parentid#)
  </insert>
  <insert id="addParam"  parameterClass="DefinedParam">
    INSERT INTO param (id,indexid,paramname,type_id,input_type,input_ename,input_length,input_enum,posttime,updatetime,userid,search,searchvalue,unitvalue,mustfill,status,feild1,feild2,article_url,pictag,issample,parentid,isshow) 
       values (#id#,#indexid#,#paramname#,#type_id#,#input_type#,#input_ename#,#input_length#,#input_enum#,now(),now(),#userid#,#search#,#searchvalue#,#unitvalue#,#mustfill#,#status#,#feild1#,#feild2#,#article_url#,#pictag#,#issample#,#parentid#,#isshow#)
  </insert>
  <select id="getParamType" resultClass="row" parameterClass="java.lang.String">
  	select * from param_type where status = 1 and parentid = #parentid# order by indexid desc;
  </select>
  <select id="getParamByTypeId" resultClass="row" parameterClass="java.lang.Integer">
  	select * from param where type_id = #id# and status = 1 order by indexid desc;
  </select>
  <select id="getParamByRow" resultClass="row" parameterClass="row">
  	select * from param where type_id = #type_id# and status = 1 
 	<isNotNull prepend="and" property="issample">
		<![CDATA[issample=#issample#]]>
   </isNotNull>
   <isNotNull prepend="and" property="isshow">
		<![CDATA[isshow=#isshow#]]>
   </isNotNull>
  	order by indexid desc;
  </select>
  <select id="getParamByName" resultClass="row" parameterClass="row">
  	select * from param where parentid=#channel# and paramname=#paramname# and status=1;
  </select>
  <select id="getInputNameAll" resultClass="java.lang.String" >
  	select input_ename from param where status = 1 and parentid=#parentid#;
  </select>
  <select id="getInputNameBySearch" resultClass="java.lang.String" >
  	select input_ename from param where status = 1 and search = #search#;
  </select>
  <select id="getParamById" resultClass="row" parameterClass="java.lang.Integer">
  	select * from param where id=#id#;
  </select>
  <select id="getParamTypeById" resultClass="row" parameterClass="java.lang.Integer">
  	select * from param_type where id=#id#;
  </select>
  <select id="getParamShowCount" resultClass="java.lang.Integer" parameterClass="java.lang.String">
  	select count(*) from param a left join param_type b on a.type_id=b.id where a.isshow = 1 and a.status=1 and b.parentid=#parentid#;
  </select>
  <select id="getParamSampleCount" resultClass="java.lang.Integer" parameterClass="java.lang.String">
  	select count(*) from param a left join param_type b on a.type_id=b.id where a.issample = 1 and a.status=1 and b.parentid=#parentid#;
  </select>
  <select id="checkParamExist" resultClass="java.lang.Integer" parameterClass="java.lang.String">
  	select count(*) from param where input_ename like #param#
  </select>
  <update id="updateParamType"  parameterClass="DefinedParamType">
     UPDATE param_type
     <dynamic prepend="SET">
     <isNotNull prepend="," property="indexid">
			<![CDATA[indexid=#indexid#]]>
	   </isNotNull>
	     <isNotNull prepend="," property="typename">
			<![CDATA[typename=#typename#]]>
	   </isNotNull>
	     <isNotNull prepend="," property="descri">
			<![CDATA[descri=#descri#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="db_id">
			<![CDATA[db_id=#db_id#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="pic_id">
			<![CDATA[pic_id=#pic_id#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="article">
			<![CDATA[article=#article#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="status">
			<![CDATA[status=#status#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="parentid">
			<![CDATA[parentid=#parentid#]]>
	   </isNotNull>
     </dynamic>
     , updatetime=NOW(),
     userid=#userid#
     where id=#id#
	</update>
  <update id="updateParam"  parameterClass="DefinedParam">
     UPDATE param
     <dynamic prepend="SET">
     <isNotNull prepend="," property="indexid">
			<![CDATA[indexid=#indexid#]]>
	   </isNotNull>
	     <isNotNull prepend="," property="paramname">
			<![CDATA[paramname=#paramname#]]>
	   </isNotNull>
	     <isNotNull prepend="," property="type_id">
			<![CDATA[type_id=#type_id#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="input_type">
			<![CDATA[input_type=#input_type#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="input_ename">
			<![CDATA[input_ename=#input_ename#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="input_length">
			<![CDATA[input_length=#input_length#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="input_enum">
			<![CDATA[input_enum=#input_enum#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="search">
			<![CDATA[search=#search#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="searchvalue">
			<![CDATA[searchvalue=#searchvalue#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="unitvalue">
			<![CDATA[unitvalue=#unitvalue#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="mustfill">
			<![CDATA[mustfill=#mustfill#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="status">
			<![CDATA[status=#status#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="pictag">
			<![CDATA[pictag=#pictag#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="article_url">
			<![CDATA[article_url=#article_url#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="issample">
			<![CDATA[issample=#issample#]]>
	   </isNotNull>
	   <isNotNull prepend="," property="isshow">
			<![CDATA[isshow=#isshow#]]>
	   </isNotNull>
     </dynamic>
     , updatetime=NOW(),
     userid=#userid#
     where id=#id#
	</update>
</sqlMap>