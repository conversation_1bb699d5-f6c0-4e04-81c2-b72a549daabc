<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="keyframe">
	<typeAlias alias="videokeyframe" type="com.cc.ovp.domain.Videokeyframe" />
	<typeAlias alias="doc" type="ayou.util.DOC" />
	<sql id="_columns">
		id,video_pool_id,keytime,keycontext,addtime,userid,status,houts,minutes,seconds,btnHref,btnDesc,btnSettingSwitch
	</sql>
	
	<insert id="insert" parameterClass="videokeyframe">
		replace into video_keyframe (id,video_pool_id,keytime,keycontext,userid,status,houts,minutes,seconds) 
	    values (#id#, #video_pool_id#, #keytime#, #keycontext#,#userid#,#status#,#houts#,#minutes#,#seconds#)
	</insert>
	<!-- 取得全部 -->
	<select id="selectList" parameterClass="java.util.Map" resultClass="videokeyframe">
		select <include refid="_columns"/> from video_keyframe where 1=1
		<isPropertyAvailable property="id"> 
				<isNotEmpty prepend="AND" property="id"> 
					id = #id# 
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="video_pool_id"> 
				<isNotEmpty prepend="AND" property="video_pool_id"> 
					<![CDATA[ video_pool_id = #video_pool_id# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="keytime"> 
				<isNotEmpty prepend="AND" property="keytime"> 
					<![CDATA[ keytime = #keytime# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="keycontext"> 
				<isNotEmpty prepend="AND" property="keycontext"> 
					<![CDATA[ keycontext = #keycontext# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[ userid = #userid# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="status"> 
				<isNotEmpty prepend="AND" property="status"> 
					<![CDATA[ status = #status# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
						<isPropertyAvailable property="seconds"> 
				<isNotEmpty prepend="AND" property="seconds"> 
					<![CDATA[ seconds = #seconds# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
						<isPropertyAvailable property="minutes"> 
				<isNotEmpty prepend="AND" property="minutes"> 
					<![CDATA[ minutes = #minutes# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
						<isPropertyAvailable property="houts"> 
				<isNotEmpty prepend="AND" property="houts"> 
					<![CDATA[ houts = #houts# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
        
        <dynamic prepend="order by">
            <isNotEmpty property="orderByClause">$orderByClause$</isNotEmpty>
            <isNotEmpty property="sortname">$sortname$</isNotEmpty>
            <isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
        </dynamic>
        
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
		<update id="update" parameterClass="videokeyframe">
		update video_keyframe set
		
		<isPropertyAvailable property="video_pool_id"> 
			<isNotEmpty property="video_pool_id"> 
				video_pool_id = #video_pool_id# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
		
		<isPropertyAvailable property="userid"> 
			<isNotEmpty property="userid"> 
				userid = #userid# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="keytime"> 
			<isNotEmpty property="keytime"> 
				keytime = #keytime# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="keycontext"> 
			<isNotEmpty property="keycontext"> 
				keycontext = #keycontext# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="status"> 
			<isNotEmpty property="status"> 
				status = #status# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	       	    <isPropertyAvailable property="seconds"> 
			<isNotEmpty property="seconds"> 
				seconds = #seconds# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	       	    <isPropertyAvailable property="minutes"> 
			<isNotEmpty property="minutes"> 
				minutes = #minutes# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	       	    <isPropertyAvailable property="houts"> 
			<isNotEmpty property="houts"> 
				houts = #houts# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	       	    
		id = #id# 
		where id = #id#
	</update>
	
	<delete id="delete" parameterClass="long">
		delete from video_keyframe where id = #id#
	</delete>
	
	<select id="getByid" parameterClass="long" resultClass="videokeyframe">
		select <include refid="_columns"/> from video_keyframe where id = #id#
	</select>
	
		<select id="getCount" parameterClass="doc" resultClass="int">
		select count(*) from video_keyframe where 1=1
				<isPropertyAvailable property="id"> 
				<isNotEmpty prepend="AND" property="id"> 
					id = #id# 
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="video_pool_id"> 
				<isNotEmpty prepend="AND" property="video_pool_id"> 
					<![CDATA[ video_pool_id = #video_pool_id# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="keytime"> 
				<isNotEmpty prepend="AND" property="keytime"> 
					<![CDATA[ keytime = #keytime# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="keycontext"> 
				<isNotEmpty prepend="AND" property="keycontext"> 
					<![CDATA[ keycontext = #keycontext# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[ userid = #userid# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="status"> 
				<isNotEmpty prepend="AND" property="status"> 
					<![CDATA[ status = #status# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
		   <isPropertyAvailable property="seconds"> 
				<isNotEmpty prepend="AND" property="seconds"> 
					<![CDATA[ seconds = #seconds# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
						<isPropertyAvailable property="minutes"> 
				<isNotEmpty prepend="AND" property="minutes"> 
					<![CDATA[ minutes = #minutes# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
						<isPropertyAvailable property="houts"> 
				<isNotEmpty prepend="AND" property="houts"> 
					<![CDATA[ houts = #houts# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
	</select>
	
</sqlMap>
