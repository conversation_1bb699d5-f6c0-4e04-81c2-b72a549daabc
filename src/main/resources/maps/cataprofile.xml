<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="cataprofile">
	<typeAlias alias="cataprofile" type="com.cc.ovp.domain.CataProfile" />
	
	<sql id="_columns">
		cataid,userid,playerid,ext,createtime,status,name
	</sql>
	
	<insert id="insert" parameterClass="cataprofile">
		insert into cata_profile (cataid,userid,playerid,ext,createtime,status,name) values	(#cataid#,#userid#,#playerid#,#ext#,#createtime#,#status#,#name#)
	</insert>

	<update id="update" parameterClass="cataprofile">
		update cata_profile set playerid = #playerid#,status = #status#, ext = #ext#,name=#name# where cataid = #cataid#
	</update>
	
	<delete id="delete" parameterClass="String">
		delete from cata_profile where cataid = #cataid#
	</delete>
	

	<select id="selectBycataId" parameterClass="String" resultClass="cataprofile">
		select * from cata_profile where cataid = #cataid#
	</select>
	
	<select id="selectBycataIdUserId" parameterClass="java.util.Map" resultClass="cataprofile">
		select * from cata_profile where 1=1 
		<isPropertyAvailable property="cataid"> 
				<isNotEmpty prepend="AND" property="cataid"> 
					<![CDATA[cataid = #cataid# ]]>
				</isNotEmpty>
		</isPropertyAvailable>
		<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
		</isPropertyAvailable>
	</select>
	
	<select id="selectList" resultClass="cataprofile">
		select <include refid="_columns"/> from cata_profile where 1=1 
		
		<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
		</isPropertyAvailable>
			
			
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount" resultClass="int">
		select count(*) from cata_profile
	</select>	

</sqlMap>
