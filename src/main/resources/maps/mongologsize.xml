<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="MongoLogSize">
	<typeAlias alias="mls" type="com.cc.ovp.domain.MongoLogSize" />
	<!-- 查询是否已经有记录 -->
	<select id="query"  resultClass="mls">
		select * from mongo_log where id='12345678';
	</select>
	
	<select id="getAdLog"  resultClass="mls">
		select * from mongo_log where id='001';
	</select>
	
	<!-- 第一次,创建访问ip记录 -->
	<insert id="insert" parameterClass="mls">
		insert into mongo_log (id,size,ptime,pid) values (#id#,#size#,#ptime#,#pid#)
	</insert>
	
	<insert id="insertAdLog" parameterClass="mls">
		insert into mongo_log (id,size,ptime,pid) values (#id#,#size#,#ptime#,#pid#)
	</insert>

	<!-- 更新记录 -->
	<update id="update" parameterClass="mls">
		update mongo_log set size=#size#,ptime=#ptime#,pid=#pid# where id='12345678'
	</update>
	
	<update id="updateAdLog" parameterClass="mls">
		update mongo_log set size=#size#,ptime=#ptime#,pid=#pid# where id='001';
	</update>
</sqlMap>
