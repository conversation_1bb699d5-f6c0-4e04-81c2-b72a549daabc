<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="space_stat">
	<typeAlias alias="space" type="com.cc.ovp.domain.Space" />
	<typeAlias alias="spaceDateEntity" type="com.cc.ovp.domain.entity.SpaceDateEntity" />
	<!-- 查询是否已经有记录 -->
	<select id="select" parameterClass="space" resultClass="space">
		select * from space_stat where indate =#indate# and userid=#userid#
	</select>
	<!-- 查询某个时间段的空间使用情况 -->
	<select id="countSizeByTime" parameterClass="spaceDateEntity" resultClass="long">
		<![CDATA[select sum(diskspace) as size from space_stat 
        where userid=#userid# and indate>=#startDate# and indate<=#endDate#]]>
	</select>

	<!-- 查询一天的 -->
	<select id="sumSizeOfOneDay" parameterClass="java.util.Map" resultClass="long">
		<![CDATA[select diskspace as size from space_stat
        where userid=#userid# and indate = #date#]]>
	</select>

	<!-- 查询多个用户一天的 -->
	<select id="sumSizeOfOneDayBatch" parameterClass="java.util.Map" resultClass="com.cc.ovp.vo.UserResourceUsageChange">
		SELECT userid, SUM(diskspace) AS `change` FROM space_stat
		WHERE
		userid IN
		<iterate property="userIds" conjunction="," close=")" open="(" >
			#userIds[]#
		</iterate>
		AND indate = #date#
		GROUP BY userid
	</select>
	
	<select id="getAll" parameterClass="java.util.Map" resultClass="long">
		select sum(diskspace) as size from space_stat where userid=#userid# 
	</select>
	
	<select id="getTot"  resultClass="long">
		select sum(diskspace) from space_stat 
	</select>
	<!-- 增加用户空间 -->
	<update id="addspace" parameterClass="space">
		update space_stat set diskspace=#diskspace# where userid=#userid# and indate=#indate#
	</update>
	
	<!-- 第一次,创建记录 -->
	<insert id="insert" parameterClass="space">
		insert into space_stat (userid, indate, logintnum, videonum, lmodify, status, 
	diskspace) values (#userid#, #indate#, #logintnum#, #videonum#, #lmodify#, #status#, 
	#diskspace#)
	</insert>

	<!-- 更新记录 -->
	<update id="login" parameterClass="space">
		update space_stat set logintnum=#logintnum# where  userid=#userid# and  indate =#indate#
	</update>
	
	<update id="video" parameterClass="space">
		update space_stat set videonum=#videonum# where indate =#indate# and userid=#userid#
	</update>
	<update id="update" parameterClass="space">
		update space_stat set videonum=#videonum# ,videonum=#videonum#,lmodify=#lmodify#,status=#status#,
		diskspace=#diskspace#,logintnum=#logintnum# where indate =#indate# and userid=#userid#
	</update>
	<select id="getcount" parameterClass="java.util.Map" resultClass="int">
		<![CDATA[select count(userid)  from space_stat 
		where datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0 and
        userid=#userid#]]>
	</select>
	<!-- 获得两个日期间的上传总数 -->
	<select id="getuptimes" parameterClass="java.util.Map" resultClass="int">
		<![CDATA[select count(videonum)  from space_stat 
		where datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0 and
        userid=#userid#]]>
	</select>
	<select id="spacelist" parameterClass="java.util.Map" resultClass="space">
		<![CDATA[select *  from space_stat 
		where datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0 and userid=#userid#]]>
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getfirsttime" parameterClass="String" resultClass="String">
		select  space_stat.indate  from space_stat 
		where userid=#userid#  limit 0,1;
	</select>

	<select id="getSumSpace" parameterClass="java.util.Map" resultClass="int">
		<![CDATA[select count(userid)  from space_stat
				 where datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0 and
					 userid=#userid#]]>
	</select>

</sqlMap>
