<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="changePeriod">
    <typeAlias alias="changePeriod" type="com.cc.AgentPlatform.domain.ChangePeriod" />
    
    <sql id="_columns">
        userId,modifyTime,period
    </sql>
    
    <insert id="insert" parameterClass="changePeriod">
        insert into change_period (userId,modifyTime,period) values (#userId#,#modifyTime#,#period#)
    </insert>
    
    <update id="update" parameterClass="changePeriod">
        update change_period set modifyTime = #modifyTime#,period = #period# where userId = #userId#
    </update>    
    
    <delete id="delete" parameterClass="String">
        delete from change_period where userId = #userId#
    </delete>   
    
    <select id="selectList" resultClass="changePeriod">
        select <include refid="_columns"/> from change_period where 1=1 
        
        <isPropertyAvailable property="userId"> 
                <isNotEmpty prepend="AND" property="userId"> 
                    <![CDATA[userId = #userId# ]]>
                </isNotEmpty>
        </isPropertyAvailable>
        
        <dynamic prepend="limit">
            <isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
            <isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
        </dynamic>
    </select>
</sqlMap>