<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="unionUser">

    <typeAlias alias="unionUser" type="com.cc.usercenter.domain.UnionUser" />

    <resultMap id="BaseResultMap" class="com.cc.usercenter.domain.UnionUser">
        <result column="unionid" jdbcType="VARCHAR" property="unionid" />
        <result column="vod_userid" jdbcType="VARCHAR" property="vodUserid" />
        <result column="live_userid" jdbcType="VARCHAR" property="liveUserid" />
        <result column="email" jdbcType="VARCHAR" property="email" />
        <result column="passwd" jdbcType="VARCHAR" property="passwd" />
        <result column="vod_passwd" jdbcType="VARCHAR" property="vodPasswd" />
        <result column="live_passwd" jdbcType="VARCHAR" property="livePasswd" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="contact" jdbcType="VARCHAR" property="contact" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="qq" jdbcType="VARCHAR" property="qq" />
        <result column="openid" jdbcType="VARCHAR" property="openid" />
        <result column="industry" jdbcType="VARCHAR" property="industry" />
        <result column="country" jdbcType="VARCHAR" property="country" />
        <result column="province" jdbcType="VARCHAR" property="province" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="note" jdbcType="VARCHAR" property="note" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="date_created" jdbcType="TIMESTAMP" property="dateCreated" />
        <result column="date_modified" jdbcType="TIMESTAMP" property="dateModified" />
        <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
        <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
        <result column="login_location" jdbcType="VARCHAR" property="loginLocation" />
        <result column="from_where" jdbcType="VARCHAR" property="fromWhere" />
        <result column="inviter" jdbcType="VARCHAR" property="inviter" />
        <result column="register_source" jdbcType="VARCHAR" property="registerSource" />
        <result column="stuRange" jdbcType="VARCHAR" property="sturange" />
    </resultMap>

    <sql id="Base_Column_List">
        unionid, vod_userid, live_userid, email, passwd, vod_passwd, live_passwd, name, contact,
        mobile, qq, openid, industry, country, province, city, note, status, date_created,
        date_modified, login_ip, login_time, login_location, from_where, inviter, register_source,
        stuRange
    </sql>

    <select id="findByVodUserId" parameterClass="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM union_user
        WHERE vod_userid = #vodUserId#
    </select>

    <select id="getUnionIdByVodUserId" parameterClass="java.lang.String" resultClass="String">
        SELECT unionid
        FROM union_user
        WHERE vod_userid = #vodUserId#
    </select>

    <select id="getByVodUserIds" parameterClass="java.util.List" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM union_user
        WHERE vod_userid IN
        <iterate open="(" close=")" conjunction=",">
            #vodUserIds[]#
        </iterate>
    </select>

</sqlMap>

