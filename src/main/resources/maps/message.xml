<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="system_message">

	<typeAlias alias="message" type="com.cc.ovp.domain.Message" />
	<typeAlias alias="messageEntity" type="com.cc.ovp.domain.entity.MessageEntity" />
	<sql id="_columns">
		id, title, sender, receiver, content, ptime, imageurl,status
	</sql>
	
	<insert id="insert" parameterClass="message">
		insert into system_message 
		(id,title,sender, receiver, content, ptime, imageurl,status)
		values(#id#,#title#, #sender#, #receiver#, #content#,#ptime#,#imageUrl#,#status#)
	</insert>

	<update id="update" parameterClass="message">
		update system_message set
		title = #title#, content = #content#, sender = #sender#, ptime = #ptime#
		where id = #id#
	</update>
	
	<delete id="delete" parameterClass="String">
		delete from system_message where id = #id#
	</delete>
	
	<select id="selectById" parameterClass="String" resultClass="message">
		select <include refid="_columns"/> from system_message where id = #id#
	</select>
	
	<select id="selectList" parameterClass="java.util.Map" resultClass="message">
		select id, title, content, sender,receiver,imageurl,status, ptime  from system_message
		<dynamic prepend="where">
			<isNotEmpty property="receiver">receiver=#receiver# and status=1</isNotEmpty>
		</dynamic>
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">#sortname#</isNotEmpty>
			<isNotEmpty property="sortorder">  #sortorder#</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListBySender" parameterClass="java.util.Map" resultClass="message">
		select id, title, content, sender,receiver,imageurl,status, ptime  from system_message
		<dynamic prepend="where">
			<isNotEmpty property="sender">sender=#sender# and status=1</isNotEmpty>
		</dynamic>
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">#sortname#</isNotEmpty>
			<isNotEmpty property="sortorder">  #sortorder#</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount" parameterClass="String" resultClass="int">
		select count(*) from system_message where receiver=#email#
	</select>
	<select id="selectCountBySender" parameterClass="String" resultClass="int">
		select count(*) from system_message where sender=#email#
	</select>

</sqlMap>
