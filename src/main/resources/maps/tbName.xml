<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tbName">

	<typeAlias alias="tbName" type="com.cc.ovp.domain.TbName" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							id,
							name,
							del,
							addtime
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ tb_name.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="name"> 
									<isNotEmpty prepend="AND" property="name"> 
										<![CDATA[ tb_name.name = #name# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ tb_name.del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="addtime"> 
									<isNotEmpty prepend="AND" property="addtime"> 
										<![CDATA[tb_name.addtime<DATE_FORMAT( '$addtime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="tbName">
		insert into tb_name (id,name,del,addtime)values(#id#,#name#,#del#,#addtime#)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from tb_name where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="tbName">
		select <include refid="_columns"/> from tb_name where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="tbName">
		select <include refid="_columns"/> from tb_name where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
		select <include refid="_columns"/> from tb_name where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="tbName">
		select <include refid="_columns"/> from tb_name where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from tb_name where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="tbName">
		select <include refid="_columns"/> from tb_name
	</select>
	
		<update id="update" parameterClass="tbName">
		update tb_name set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="name"> 
							<isNotEmpty property="name"> 
								name = #name# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="addtime"> 
							<isNotEmpty property="addtime"> 
								addtime = #addtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

