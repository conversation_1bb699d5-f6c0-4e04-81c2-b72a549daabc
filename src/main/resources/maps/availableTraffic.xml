<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="availableTraffic">

	<typeAlias alias="availableTraffic" type="com.cc.AgentPlatform.domain.AvailableTraffic" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							id,
							userid,
							flow,
							cmoney,
							del,
							datetime,
							consumption,
							Payment,
							expirationtime,
							buyflow,
							consumptionflow,
							lastrecharge,
							space
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="flow"> 
									<isNotEmpty prepend="AND" property="flow"> 
										<![CDATA[ flow = #flow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="cmoney"> 
									<isNotEmpty prepend="AND" property="cmoney"> 
										<![CDATA[ cmoney = #cmoney# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="datetime"> 
									<isNotEmpty prepend="AND" property="datetime"> 
										<![CDATA[datetime<DATE_FORMAT( '$datetime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="consumption"> 
									<isNotEmpty prepend="AND" property="consumption"> 
										<![CDATA[ consumption = #consumption# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="Payment"> 
									<isNotEmpty prepend="AND" property="Payment"> 
										<![CDATA[ Payment = #Payment# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="expirationtime"> 
									<isNotEmpty prepend="AND" property="expirationtime"> 
										<![CDATA[expirationtime<DATE_FORMAT( '$expirationtime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="buyflow"> 
									<isNotEmpty prepend="AND" property="buyflow"> 
										<![CDATA[ buyflow = #buyflow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="consumptionflow"> 
									<isNotEmpty prepend="AND" property="consumptionflow"> 
										<![CDATA[ consumptionflow = #consumptionflow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="space"> 
									<isNotEmpty prepend="AND" property="space"> 
										<![CDATA[ space = #space# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="lastrecharge"> 
									<isNotEmpty prepend="AND" property="lastrecharge"> 
										<![CDATA[lastrecharge<DATE_FORMAT( '$lastrecharge$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="availableTraffic">
		insert into available_traffic 
		(
							id,
							userid,
							flow,
							cmoney,
							del,
							datetime,
							consumption,
							Payment,
							expirationtime,
							buyflow,
							consumptionflow,
							lastrecharge,
							space
		)
		values
		(
							#id#,
							#userid#,
							#flow#,
							#cmoney#,
							#del#,
							#datetime#,
							#consumption#,
							#Payment#,
							#expirationtime#,
							#buyflow#,
							#consumptionflow#,
							#lastrecharge#,
							#space#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from available_traffic where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="availableTraffic">
		select <include refid="_columns"/> from available_traffic where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="availableTraffic">
		select <include refid="_columns"/> from available_traffic where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="availableTraffic">
		select <include refid="_columns"/> from available_traffic where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from available_traffic where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="availableTraffic">
		select <include refid="_columns"/> from available_traffic
	</select>
	
		<update id="update" parameterClass="availableTraffic">
		update available_traffic set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="flow"> 
							<isNotEmpty property="flow"> 
								flow = #flow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="cmoney"> 
							<isNotEmpty property="cmoney"> 
								cmoney = #cmoney# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="datetime"> 
							<isNotEmpty property="datetime"> 
								datetime = #datetime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="consumption"> 
							<isNotEmpty property="consumption"> 
								consumption = #consumption# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="Payment"> 
							<isNotEmpty property="Payment"> 
								Payment = #Payment# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="expirationtime"> 
							<isNotEmpty property="expirationtime"> 
								expirationtime = #expirationtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="buyflow"> 
							<isNotEmpty property="buyflow"> 
								buyflow = #buyflow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="consumptionflow"> 
							<isNotEmpty property="consumptionflow"> 
								consumptionflow = #consumptionflow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="lastrecharge"> 
							<isNotEmpty property="lastrecharge"> 
								lastrecharge = #lastrecharge# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="space"> 
							<isNotEmpty property="space"> 
								space = #space# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>
    
    <!-- 延长试用套餐的过期时间 -->
    <update id="extendExpiredDateByUserId" parameterClass="availableTraffic">
        update available_traffic set 
        <isPropertyAvailable property="expirationtime">
            <isNotEmpty property="expirationtime"> 
                expirationtime = #expirationtime# , 
            </isNotEmpty>
        </isPropertyAvailable>
        <isPropertyAvailable property="lastrecharge">
            <isNotEmpty property="lastrecharge">
                lastrecharge = #lastrecharge# 
            </isNotEmpty>
        </isPropertyAvailable>
        where userid = #userid#
    </update>


</sqlMap>

