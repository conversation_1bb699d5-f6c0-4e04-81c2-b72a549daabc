<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="videoExam">
    <typeAlias alias="videoExam" type="com.cc.ovp.domain.VideoExam" />
    <typeAlias alias="doc" type="ayou.util.DOC" />
    
    <sql id="_columns">
        exam_id AS examId, userid, video_pool_id AS videoPoolId, show_time AS showTime, 
        hours, minutes, seconds, question, choices, answer,wrong_answer as wrongAnswer, wrong_time AS wrongTime, 
        wrong_show AS wrongShow, status, skip,type,mp3url, created_time AS createdTime, illustration
    </sql>
    
    <insert id="insert" parameterClass="videoExam">
        replace into video_exam (exam_id, userid, video_pool_id, show_time, hours, minutes, seconds, 
            question, choices, answer, wrong_answer, wrong_time, wrong_show, status, created_time,skip,type,mp3url)
        values (#examId#, #userid#, #videoPoolId#, #showTime#, #hours#, #minutes#, #seconds#, 
            #question#, #choices#, #answer#, #wrongAnswer#, #wrongTime#, #wrongShow#, #status#, #createdTime#,#skip#,#type#,#mp3url#)
    </insert>
    
    <delete id="delete" parameterClass="java.lang.String">
        delete from video_exam where exam_id = #examId#
    </delete>
    
    <select id="getById" parameterClass="java.lang.String" resultClass="videoExam">
        select <include refid="_columns"/> from video_exam where exam_id = #examId#
    </select>
    
    <!-- 取得全部 -->
    <select id="selectList" parameterClass="java.util.Map" resultClass="videoExam">
        select <include refid="_columns"/>
        from video_exam 
        <!-- where video_pool_id = #videoPoolId# -->
        where video_pool_id like concat(#videoPoolId#, '%')
        <isNotNull property="groupId">
        AND group_id = #groupId#
        </isNotNull>
        
        <dynamic prepend="order by">
            <isNotEmpty property="orderByClause">$orderByClause$</isNotEmpty>
        </dynamic>
        
        <dynamic prepend="limit">
            <isNotEmpty property="offset">#offset#</isNotEmpty>
            <isNotEmpty property="limit">, #limit#</isNotEmpty>
        </dynamic>
    </select>
    
    <select id="getCount" parameterClass="java.util.Map" resultClass="int">
        select count(*)  
        from video_exam 
        where video_pool_id = #videoPoolId#
    </select>
    
</sqlMap>
