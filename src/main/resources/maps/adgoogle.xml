<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="adgoogle">

	<typeAlias alias="adgoogle" type="com.cc.ovp.domain.Adgoogle" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	  <resultMap id="adgoogleResult" class="com.cc.ovp.domain.Adgoogle">   
					<result property="id" column="id" nullValue="0"/> 
					<result property="account" column="account"/>   
					<result property="width" column="width" nullValue="0"/>   
					<result property="height" column="height" nullValue="0"/>   
					<result property="userid" column="userid"/>   
					<result property="titlestimelength" column="titlestimelength" nullValue="0"/>   
					<result property="titlechannelid" column="titlechannelid"/>   
					<result property="titletype" column="titletype"/>   
					<result property="stopchannelid" column="stopchannelid"/>   
					<result property="stoptype" column="stoptype"/>   
					<result property="patchtimelength" column="patchtimelength" nullValue="0"/>   
					<result property="patchchannelid" column="patchchannelid"/>   
					<result property="creditstimelength" column="creditstimelength" nullValue="0"/>   
					<result property="creditschannelid" column="creditschannelid"/>   
					<result property="creditstype" column="creditstype"/>   
					<result property="del" column="del" nullValue="0"/>  
					<result property="addtime" column="addtime" nullValue="2012/01/01"/>  
					<result property="disabled" column="disabled" nullValue="0"/>  
		  
		  </resultMap>
	
	<sql id="_columns">
							adgoogle.id,
							adgoogle.account,
							adgoogle.width,
							adgoogle.height,
							adgoogle.userid,
							adgoogle.titlestimelength,
							adgoogle.titlechannelid,
							adgoogle.titletype,
							adgoogle.stopchannelid,
							adgoogle.stoptype,
							adgoogle.patchtimelength,
							adgoogle.patchchannelid,
							adgoogle.creditstimelength,
							adgoogle.creditschannelid,
							adgoogle.creditstype,
							adgoogle.del,
							adgoogle.addtime,
							adgoogle.disabled
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="disabled"> 
									<isNotEmpty prepend="AND" property="disabled"> 
										<![CDATA[ adgoogle.disabled = #disabled# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ adgoogle.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="account"> 
									<isNotEmpty prepend="AND" property="account"> 
										<![CDATA[ adgoogle.account = #account# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="width"> 
									<isNotEmpty prepend="AND" property="width"> 
										<![CDATA[ adgoogle.width = #width# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="height"> 
									<isNotEmpty prepend="AND" property="height"> 
										<![CDATA[ adgoogle.height = #height# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ adgoogle.userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="titlestimelength"> 
									<isNotEmpty prepend="AND" property="titlestimelength"> 
										<![CDATA[ adgoogle.titlestimelength = #titlestimelength# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="titlechannelid"> 
									<isNotEmpty prepend="AND" property="titlechannelid"> 
										<![CDATA[ adgoogle.titlechannelid = #titlechannelid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="titletype"> 
									<isNotEmpty prepend="AND" property="titletype"> 
										<![CDATA[ adgoogle.titletype = #titletype# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="stopchannelid"> 
									<isNotEmpty prepend="AND" property="stopchannelid"> 
										<![CDATA[ adgoogle.stopchannelid = #stopchannelid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="stoptype"> 
									<isNotEmpty prepend="AND" property="stoptype"> 
										<![CDATA[ adgoogle.stoptype = #stoptype# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="patchtimelength"> 
									<isNotEmpty prepend="AND" property="patchtimelength"> 
										<![CDATA[ adgoogle.patchtimelength = #patchtimelength# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="patchchannelid"> 
									<isNotEmpty prepend="AND" property="patchchannelid"> 
										<![CDATA[ adgoogle.patchchannelid = #patchchannelid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="creditstimelength"> 
									<isNotEmpty prepend="AND" property="creditstimelength"> 
										<![CDATA[ adgoogle.creditstimelength = #creditstimelength# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="creditschannelid"> 
									<isNotEmpty prepend="AND" property="creditschannelid"> 
										<![CDATA[ adgoogle.creditschannelid = #creditschannelid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="creditstype"> 
									<isNotEmpty prepend="AND" property="creditstype"> 
										<![CDATA[ adgoogle.creditstype = #creditstype# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ adgoogle.del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="addtime"> 
									<isNotEmpty prepend="AND" property="addtime"> 
										<![CDATA[adgoogle.addtime<DATE_FORMAT( '$addtime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="adgoogle">
		insert into adgoogle 
		(
							id,
							account,
							width,
							height,
							userid,
							titlestimelength,
							titlechannelid,
							titletype,
							stopchannelid,
							stoptype,
							patchtimelength,
							patchchannelid,
							creditstimelength,
							creditschannelid,
							creditstype,
							del,
							addtime,
							disabled
		)
		values
		(
							#id#,
							#account#,
							#width#,
							#height#,
							#userid#,
							#titlestimelength#,
							#titlechannelid#,
							#titletype#,
							#stopchannelid#,
							#stoptype#,
							#patchtimelength#,
							#patchchannelid#,
							#creditstimelength#,
							#creditschannelid#,
							#creditstype#,
							#del#,
							#addtime#,
							#disabled#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from adgoogle where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultMap="adgoogleResult">
		select <include refid="_columns"/> from adgoogle where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultMap="adgoogleResult">
		select <include refid="_columns"/> from adgoogle where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
		select <include refid="_columns"/> from adgoogle where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultMap="adgoogleResult">
		select <include refid="_columns"/> from adgoogle where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from adgoogle where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultMap="adgoogleResult">
		select <include refid="_columns"/> from adgoogle
	</select>
	
		<update id="update" parameterClass="adgoogle">
		update adgoogle set
		
						<isPropertyAvailable property="disabled"> 
							<isNotEmpty property="disabled"> 
								disabled = #disabled# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="account"> 
							<isNotEmpty property="account"> 
								account = #account# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="width"> 
							<isNotEmpty property="width"> 
								width = #width# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="height"> 
							<isNotEmpty property="height"> 
								height = #height# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="titlestimelength"> 
							<isNotEmpty property="titlestimelength"> 
								titlestimelength = #titlestimelength# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="titlechannelid"> 
							<isNotEmpty property="titlechannelid"> 
								titlechannelid = #titlechannelid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="titletype"> 
							<isNotEmpty property="titletype"> 
								titletype = #titletype# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="stopchannelid"> 
							<isNotEmpty property="stopchannelid"> 
								stopchannelid = #stopchannelid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="stoptype"> 
							<isNotEmpty property="stoptype"> 
								stoptype = #stoptype# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="patchtimelength"> 
							<isNotEmpty property="patchtimelength"> 
								patchtimelength = #patchtimelength# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="patchchannelid"> 
							<isNotEmpty property="patchchannelid"> 
								patchchannelid = #patchchannelid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="creditstimelength"> 
							<isNotEmpty property="creditstimelength"> 
								creditstimelength = #creditstimelength# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="creditschannelid"> 
							<isNotEmpty property="creditschannelid"> 
								creditschannelid = #creditschannelid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="creditstype"> 
							<isNotEmpty property="creditstype"> 
								creditstype = #creditstype# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="addtime"> 
							<isNotEmpty property="addtime"> 
								addtime = #addtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

