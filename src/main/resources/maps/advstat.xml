<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="advstat">
	<typeAlias alias="advStat" type="com.cc.ovp.domain.AdvStat" />
	<select id="selectAdvStatByDate" parameterClass="java.util.Map" resultClass="advStat">
		<![CDATA[select *  from ad_stat where userid=#userid# and adid =#adid# 
		 and datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0]]>
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">#sortname#</isNotEmpty>
			<isNotEmpty property="sortorder">  #sortorder#</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectAdvStatCountByDate" parameterClass="java.util.Map" resultClass="int">
		select count(*) from ad_stat where  userid=#userid# and adid=#adid# and
		<![CDATA[datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0]]>
	</select>
	
	<!-- 获得用户第一次第一条记录 -->
	<select id="getFirstDate" parameterClass="java.util.Map" resultClass="String">
		select  ad_stat.indate  from play_stat 
		where userid=#userid# and adid =#adid#    limit 0,1;
	</select>
	
	<update id="addTimes" parameterClass="java.util.Map">
		update   ad_stat  set times=times+1
		where adid =#adid# and indate=#indate# and province=#province#
	</update>
	
		<select id="selectById" parameterClass="java.util.Map" resultClass="advStat">
		select  *  from ad_stat 
		where adid =#adid#  and indate=#indate# and province=#province#
	    </select>
	    
	    <insert id="insert" parameterClass="advStat">
		insert into ad_stat (adid, title, userid, indate, times, ptime,province) values 
		(#adid#, #title#, #userid#, #indate#, #times#, #ptime#,#province#)
	</insert>
	
	
	

</sqlMap>
