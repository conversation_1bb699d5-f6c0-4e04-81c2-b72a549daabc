<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="videoExamLog">
    <typeAlias alias="videoExamLog" type="com.cc.ovp.domain.VideoExamLog" />
    <typeAlias alias="doc" type="ayou.util.DOC" />
    
    <sql id="_columns">
        logid, exam_id AS examId, userid, video_pool_id AS videoPoolId, question, 
        answer, is_correct AS isCorrect, play_id AS playId, ip_address AS ipAddress, 
        province, isp, operating_system AS operatingSystem, browser, date_added AS dateAdded, viewerid as viewerId
    </sql>
    
    <insert id="insert" parameterClass="videoExamLog">
        insert into video_exam_log (exam_id, userid, video_pool_id, question, answer, is_correct, 
            play_id, ip_address, province, isp, operating_system, browser, date_added, viewerid)
        values (#examId#, #userid#, #videoPoolId#, #question#, #answer#, #isCorrect#, 
            #playId#, #ipAddress#, #province#, #isp#, #operatingSystem#, #browser#, #dateAdded#, #viewerId#)
    </insert>
    
    <delete id="delete" parameterClass="long">
        delete from video_exam_log where logid = #logid#
    </delete>
    
    <select id="getById" parameterClass="long" resultClass="videoExamLog">
        select <include refid="_columns"/> from video_exam_log where logid = #logid#
    </select>
    
    <!-- 取得全部 -->
    <select id="selectList" parameterClass="java.util.Map" resultClass="videoExamLog">
        select <include refid="_columns"/> 
        from video_exam_log 
        where video_pool_id = #videoPoolId#
        
        <dynamic prepend="order by">
            <isNotEmpty property="orderByClause">$orderByClause$</isNotEmpty>
        </dynamic>
        
        <dynamic prepend="limit">
            <isNotEmpty property="offset">#offset#</isNotEmpty>
            <isNotEmpty property="limit">, #limit#</isNotEmpty>
        </dynamic>
    </select>
    
    <select id="getCount" parameterClass="java.util.Map" resultClass="int">
        select count(*)  
        from video_exam_log 
        where video_pool_id = #videoPoolId#
    </select>
    
</sqlMap>
