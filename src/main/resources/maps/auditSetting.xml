<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="auditSetting">

	<typeAlias alias="auditSetting" type="com.cc.ovp.domain.AuditSetting" />

	<sql id="_columns">
		userId, snapshotInterval, scope, scene, enabled, createdTime, lastModified
	</sql>
	<sql id="table">
		audit_setting
	</sql>

	<select id="selectById" parameterClass="java.lang.String" resultClass="auditSetting">
		select <include refid="_columns"/> from <include refid="table"/> where userId = #userId#
	</select>

</sqlMap>
