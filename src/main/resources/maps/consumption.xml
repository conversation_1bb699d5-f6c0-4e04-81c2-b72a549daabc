<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="consumption">

	<typeAlias alias="consumption" type="com.cc.AgentPlatform.domain.Consumption" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							id,
							userid,
							cmoney,
							consumptiontime,
							del,
							datetime,
							available_traffic_id,
							starttime,
							endtime,
							consumptionflow,
							space,
							type,
							orderid
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="space"> 
									<isNotEmpty prepend="AND" property="space"> 
										<![CDATA[ space = #space# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="cmoney"> 
									<isNotEmpty prepend="AND" property="cmoney"> 
										<![CDATA[ cmoney = #cmoney# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="consumptiontime"> 
									<isNotEmpty prepend="AND" property="consumptiontime"> 
										<![CDATA[consumptiontime<DATE_FORMAT( '$consumptiontime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="type"> 
									<isNotEmpty prepend="AND" property="type"> 
										<![CDATA[ type = #type# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="datetime"> 
									<isNotEmpty prepend="AND" property="datetime"> 
										<![CDATA[datetime<DATE_FORMAT( '$datetime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="available_traffic_id"> 
									<isNotEmpty prepend="AND" property="available_traffic_id"> 
										<![CDATA[ available_traffic_id = #available_traffic_id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="starttime"> 
									<isNotEmpty prepend="AND" property="starttime"> 
										<![CDATA[starttime<DATE_FORMAT( '$starttime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="endtime"> 
									<isNotEmpty prepend="AND" property="endtime"> 
										<![CDATA[endtime<DATE_FORMAT( '$endtime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="consumptionflow"> 
									<isNotEmpty prepend="AND" property="consumptionflow"> 
										<![CDATA[consumptionflow<DATE_FORMAT( '$consumptionflow$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="consumption">
		insert into consumption 
		(
							id,
							userid,
							cmoney,
							consumptiontime,
							del,
							datetime,
							available_traffic_id,
							starttime,
							endtime,
							consumptionflow,
							space,
							type,
							orderid
		)
		values
		(
							#id#,
							#userid#,
							#cmoney#,
							#consumptiontime#,
							#del#,
							#datetime#,
							#available_traffic_id#,
							#starttime#,
							#endtime#,
							#consumptionflow#,
							#space#,
							#type#,
							#orderid#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from consumption where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="consumption">
		select <include refid="_columns"/> from consumption where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="consumption">
		select <include refid="_columns"/> from consumption where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="consumption">
		select <include refid="_columns"/> from consumption where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from consumption where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="consumption">
		select <include refid="_columns"/> from consumption
	</select>
	
		<update id="update" parameterClass="consumption">
		update consumption set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="type"> 
							<isNotEmpty property="type"> 
								"type" = #"type"# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="orderid"> 
							<isNotEmpty property="orderid"> 
								orderid = #orderid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="cmoney"> 
							<isNotEmpty property="cmoney"> 
								cmoney = #cmoney# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="consumptiontime"> 
							<isNotEmpty property="consumptiontime"> 
								consumptiontime = #consumptiontime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="datetime"> 
							<isNotEmpty property="datetime"> 
								datetime = #datetime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="available_traffic_id"> 
							<isNotEmpty property="available_traffic_id"> 
								available_traffic_id = #available_traffic_id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="starttime"> 
							<isNotEmpty property="starttime"> 
								starttime = #starttime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="endtime"> 
							<isNotEmpty property="endtime"> 
								endtime = #endtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="consumptionflow"> 
							<isNotEmpty property="consumptionflow"> 
								consumptionflow = #consumptionflow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="space"> 
							<isNotEmpty property="space"> 
								space = #space# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

