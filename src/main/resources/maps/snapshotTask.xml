<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="snapshotTask">

	<typeAlias alias="snapshotTask" type="com.cc.ovp.domain.SnapshotTask" />

	<sql id="_columns">
		video_pool_id, user_id, width, height, callback, status, begin_process_time, finish_process_time, create_time
	</sql>

	<insert id="insert" parameterClass="com.cc.ovp.domain.SnapshotTask">
		insert into snapshot_task(video_pool_id, user_id, width,
		height, callback, status,
		begin_process_time, finish_process_time,
		create_time) values (#videoPoolId#,
		#userId#, #width#,
		#height#, #callback#, #status#,
		#beginProcessTime#, #finishProcessTime#,
		#createTime#)
		<selectKey keyProperty="taskId" resultClass="java.lang.Long">
			SELECT LAST_INSERT_ID()
		</selectKey>
	</insert>


	<insert id="batchInsertImage" parameterClass="java.util.List">
		insert into snapshot_task_image (task_id, offset_time, url,create_time, update_time)
		values
		<iterate conjunction=",">
			<![CDATA[
            (#images[].taskId#, #images[].offsetTime#, #images[].url#, #images[].createTime#, #images[].updateTime#)
        ]]>
		</iterate>
	</insert>
</sqlMap>
