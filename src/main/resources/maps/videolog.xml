<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="video_log">
	<typeAlias alias="videoLog" type="com.cc.ovp.domain.VideoLog" />
	<typeAlias alias="doc" type="ayou.util.DOC" />
	<sql id="_columns">
		autoid, video_pool_id, br, definition, userid, title, ext, first_image, swk_link, 
	    percent, processip, pid, startime, endtime, ptime, times, status,logtext,logtime,hostip,hostname,isVip,field1,field2, storage_type
	</sql>
	
	<insert id="insert" parameterClass="videoLog">
		insert into $tablename$ (autoid, video_pool_id, br, definition, userid, title, ext,
	    first_image, swk_link, percent, processip, pid, ptime,startime, times,endtime, status,logtext,logtime,error,my_br,coding,isVip,field1,field2,storage_type)
	    values (#autoid#, #video_pool_id#, #br#, #definition#,#userid#,#title#, #ext#, #first_image#, 
		#swk_link#,#percent#, #processIp#, #pid#, #ptime#,#startTime#, #times#,#endTime#, #status#,#logtext#,#logtime#,#error#,#my_br#,#coding#,#isVip#, #field1#, #field2#,#storage_type#)
		on duplicate key
		update autoid=values(autoid),
		       br=values(br),
			   definition=values(definition),
			   title=values(title),
			   ext=values(ext),
			   first_image=values(first_image),
			   swk_link=values(swk_link),
			   percent=values(percent),
			   processip=values(processip),
		       ptime=values(ptime),
			   startime=values(startime),
			   endtime=values(endtime),
			   logtext=values(logtext),
			   logtime=values(logtime),
			   my_br=values(my_br),
			   coding=values(coding),
			   isVip=values(isVip),
		       status=values(status),
			   field1=values(field1),
			   field2=values(field2)
		       <isPropertyAvailable property="storage_type">
					<isNotEmpty prepend="," property="storage_type">
						storage_type=values(storage_type)
					</isNotEmpty>
			   </isPropertyAvailable>
	</insert>
	<!-- 取得全部的视频记录展示 -->
	
		<select id="selectTotalOnDeal" parameterClass="java.util.Map" resultClass="int">
		select count(userid) from $tablename$ where 1=1
		<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid">
					video_pool_id like concat(#userid#, "%")
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="videoid"> 
				<isNotEmpty prepend="AND" property="videoid"> 
					 video_pool_id = #videoid# 
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="title"> 
				<isNotEmpty prepend="AND" property="title"> 
					title like "%"#title#"%"
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="status"> 
				<isNotEmpty prepend="AND" property="status"> 
					status = #status# 
				</isNotEmpty>
			</isPropertyAvailable>
	</select>
	
	<select id="selectList" parameterClass="java.util.Map" resultClass="videoLog">
		select <include refid="_columns"/> from $tablename$ where 1=1
		<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid">
					video_pool_id like concat(#userid#, "%")
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="videoid"> 
				<isNotEmpty prepend="AND" property="videoid"> 
					 video_pool_id = #videoid# 
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="title"> 
				<isNotEmpty prepend="AND" property="title"> 
					title like "%"#title#"%"
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="status"> 
				<isNotEmpty prepend="AND" property="status"> 
					status = #status# 
				</isNotEmpty>
			</isPropertyAvailable>
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	<!-- 获得错误的列表 -->
	<select id="selectErrorList" parameterClass="java.util.Map" resultClass="videoLog">
		select <include refid="_columns"/> from $tablename$ where <![CDATA[ status >= 40 and status < 50 and status !=43 ]]>
		<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid">
					video_pool_id like concat(#userid#, "%")
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="videoid"> 
				<isNotEmpty prepend="AND" property="videoid"> 
					 video_pool_id = #videoid# 
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="title"> 
				<isNotEmpty prepend="AND" property="title"> 
					title like "%"#title#"%"
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="status"> 
				<isNotEmpty prepend="AND" property="status"> 
					status = #status# 
				</isNotEmpty>
			</isPropertyAvailable>
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	<select id="selectErrorTotal" parameterClass="java.util.Map" resultClass="int">
		select count(userid) from $tablename$ where <![CDATA[ status >= 40 and status < 50 and status !=43 ]]>
		<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid">
					video_pool_id like concat(#userid#, "%")
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="title"> 
				<isNotEmpty prepend="AND" property="title"> 
					title like "%"#title#"%"
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="status"> 
				<isNotEmpty prepend="AND" property="status"> 
					status = #status# 
				</isNotEmpty>
			</isPropertyAvailable>
	</select>

	
	<update id="setStatus" parameterClass="java.util.Map">
	    update $tablename$ set status=#status# where userid=#userid# and video_pool_id=#video_pool_id# and br=#br#
	</update>
	<update id="setAllStatusForVideo" parameterClass="java.util.Map">
	    update $tablename$ set status=#status# where video_pool_id=#video_pool_id#
	</update>
    
    <update id="changeStatus" parameterClass="java.util.Map">
        UPDATE $tablename$ SET status=#toStatus#
        WHERE status=#fromStatus# 
        <isPropertyAvailable property="video_pool_id"> 
            <isNotEmpty prepend="AND" property="video_pool_id"> 
                video_pool_id=#video_pool_id#
            </isNotEmpty>
        </isPropertyAvailable>
    </update>
    
	<update id="setStatusAndCoding" parameterClass="doc">
	    update $tablename$ set status=#status#,coding=#coding#,percent=#percent#,ptime=#ptime# where userid=#userid# and video_pool_id=#video_pool_id# and br=#br#
	</update>
	<update id="setPercent" parameterClass="java.util.Map">
	    update $tablename$ set percent=#percent# where userid=#userid# and video_pool_id=#video_pool_id# and br=#br#
	</update>
	<update id="setException" parameterClass="java.util.Map">
	    update $tablename$ set logtext=#logtext#,logtime=#logtime#,error=1 where userid=#userid# and video_pool_id=#video_pool_id# and br=#br#
	</update>
	
	<select id="select" parameterClass="java.util.Map">
	   select * from  $tablename$ 
	   where
	   userid=#userid#  and video_pool_id=#video_pool_id# and br=#br#
	</select>
	<!-- 清理错误日志信息 -->
	<update id="cleanError" parameterClass="java.util.Map">
	    update $tablename$ set logtext="none",error=0 where userid=#userid# and video_pool_id=#video_pool_id# and br=#br#
	</update>
	
	<!-- 用于记录其他日志情况，例如MQ 方便一些统计 -->
	
	<update id="setText" parameterClass="java.util.Map">
	    update $tablename$ set logtext=#logtext#,logtime=#logtime# 
	    where  userid=#userid#  and  br=#br#
	</update>
	
	<select  id="selectText" parameterClass="java.util.Map" resultClass="videoLog">
	   select * from  $tablename$ 
	   where userid=#userid# and br=#br#
	</select>
	
	<select  id="getCodingOne" parameterClass="doc" resultClass="videoLog">
	   select * from  $tablename$ 
	   where status=#status# order by ptime desc limit 1;
	</select>
	
	<select  id="getVideoLog" parameterClass="doc" resultClass="videoLog">
	   select * from  $tablename$ 
	   where  video_pool_id=#video_pool_id#  and  br=#br# limit 1;
	</select>
	<update id="setCodingFlag" parameterClass="doc">
	    update $tablename$ set coding=#coding# 
	    where  video_pool_id=#video_pool_id#  and  br=#br#
	</update>
	
</sqlMap>
