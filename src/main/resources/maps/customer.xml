<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="customer">

	<typeAlias alias="customer" type="com.cc.ovp.domain.Customer" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
		autoid, email, userid, company, password, grade, createuser,ptime, lmodify, status, lasttime, webname, weburl, province, city, name, qq, validtime, telephone, officetel,readtoken,writetoken,autoedit,sign
	</sql>
	
	<insert id="insert" parameterClass="customer">
		insert into cc_user 
		(autoid, email, userid, company, password, grade, createuser,ptime, lmodify, status, lasttime, webname, weburl, province, city, name, qq, validtime, telephone, officetel,readtoken,writetoken,autoedit)
		values
		(#autoid#, #email#, #userid#, #company#, #password#, #grade#, #createuser#,#ptime#, #lmodify#, #status#, #lasttime#, #webname#, #weburl#, #province#, #city#, #name#, #qq#, #validtime#, #telephone#, #officetel#,#readtoken#,#writetoken#,#autoedit#)
	</insert>

	<update id="update" parameterClass="customer">
		update cc_user set
		email = #email#, company = #company#, password = #password#,lmodify = #lmodify#,
		webname =#webname#,weburl =#weburl#,province =#province#,city=#city#,name=#name#,qq=#qq#,telephone=#telephone#,officetel=#officetel#, status=#status#, grade=#grade#, webname=#webname#, readtoken=#readtoken#, writetoken=#writetoken#, autoedit=#autoedit#,sign=#sign#
		where autoid = #autoid#
	</update>
	<update id="editpermiss" parameterClass="customer">
		update cc_user set
		email = #email#,lmodify = #lmodify#,
		webname =#webname#,weburl =#weburl#,status=#status#,name = #name#,
		grade=#grade#,autoedit = #autoedit#
		where autoid = #autoid#
	</update>
		<update id="edituserinfo" parameterClass="customer">
		update cc_user set
		company = #company#,lmodify = #lmodify#,
		webname =#webname#,weburl =#weburl#,province =#province#,city=#city#,name=#name#,qq=#qq#,telephone=#telephone#,
		officetel=#officetel#,autoedit=#autoedit#
		where userid = #userid#
	</update>
	
	
	
	<delete id="delete" parameterClass="String">
		delete from cc_user where userid = #userid#
	</delete>
	
	<select id="selectById" parameterClass="long" resultClass="customer">
		select <include refid="_columns"/> from cc_user where autoid = #autoid#
	</select>
	
	<select id="selectByReadtoken" parameterClass="doc" resultClass="customer">
		select <include refid="_columns"/> from cc_user where 1=1 
		<isPropertyAvailable property="readtoken"> 
				<isNotEmpty prepend="AND" property="readtoken"> 
					<![CDATA[ readtoken = #readtoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>		
			<isPropertyAvailable property="writetoken"> 
				<isNotEmpty prepend="AND" property="writetoken"> 
					<![CDATA[ writetoken = #writetoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="customer">
		select <include refid="_columns"/> from cc_user where del=0 
			<isPropertyAvailable property="email"> 
				<isNotEmpty prepend="AND" property="email"> 
					<![CDATA[email = #email# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="readtoken"> 
				<isNotEmpty prepend="AND" property="readtoken"> 
					<![CDATA[ readtoken = #readtoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>		
			<isPropertyAvailable property="writetoken"> 
				<isNotEmpty prepend="AND" property="writetoken"> 
					<![CDATA[ writetoken = #writetoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectByDelList" parameterClass="doc" resultClass="customer">
		select <include refid="_columns"/> from cc_user where del=1 
			<isPropertyAvailable property="email"> 
				<isNotEmpty prepend="AND" property="email"> 
					<![CDATA[email = #email# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="readtoken"> 
				<isNotEmpty prepend="AND" property="readtoken"> 
					<![CDATA[ readtoken = #readtoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>		
			<isPropertyAvailable property="writetoken"> 
				<isNotEmpty prepend="AND" property="writetoken"> 
					<![CDATA[ writetoken = #writetoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="customer">
		select <include refid="_columns"/> from cc_user where del=0 
			<isPropertyAvailable property="email"> 
				<isNotEmpty prepend="AND" property="email"> 
					<![CDATA[email = #email# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="readtoken"> 
				<isNotEmpty prepend="AND" property="readtoken"> 
					<![CDATA[ readtoken = #readtoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>		
			<isPropertyAvailable property="writetoken"> 
				<isNotEmpty prepend="AND" property="writetoken"> 
					<![CDATA[ writetoken = #writetoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount" resultClass="int">
		select count(*) from cc_user where del=0 
			<isPropertyAvailable property="email"> 
				<isNotEmpty prepend="AND" property="email"> 
					<![CDATA[email = #email# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="readtoken"> 
				<isNotEmpty prepend="AND" property="readtoken"> 
					<![CDATA[ readtoken = #readtoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>		
			<isPropertyAvailable property="writetoken"> 
				<isNotEmpty prepend="AND" property="writetoken"> 
					<![CDATA[ writetoken = #writetoken# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
	</select>
	<select id="selectByEmail" parameterClass="String" resultClass="customer">
		select <include refid="_columns"/> from cc_user
		where email=#email#
	</select>
	
	<select id="selectByUserid" parameterClass="String" resultClass="customer">
		select <include refid="_columns"/> from cc_user
		where userid=#userid#
	</select>
	
	<select id="all"  resultClass="customer">
		select <include refid="_columns"/> from cc_user
	</select>
	
	<!-- 
	<![CDATA[ select * from (
		select t.*, rownum rn from (]]>
		select 
		<include refid="_columns"/> from uc_adminlog
		<dynamic prepend="where">
			<isNotEmpty property="username">
				<![CDATA[username=#username#]]>
			</isNotEmpty>
		</dynamic>
		<![CDATA[ order by posttime desc ]]>
		<![CDATA[ ) t where rownum <= #start# + #size# ) where rn > #start# ]]>
	-->

</sqlMap>
