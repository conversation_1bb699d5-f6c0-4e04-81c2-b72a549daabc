<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="m3u8content">
	<typeAlias alias="m3u8content" type="com.cc.ovp.domain.M3u8Content" />
	
	<sql id="_columns">
		videoPoolId,bitRateIndex,status,content,hexKey,dateAdded
	</sql>
	
	<insert id="insert" parameterClass="m3u8content">
		insert into m3u8_content (videoPoolId,bitRateIndex,status,content,hexKey,dateAdded) values	(#videoPoolId#,#bitRateIndex#,#status#,#content#,#hexKey#,#dateAdded#)
	</insert>

	<update id="updateStatus" parameterClass="m3u8content">
		update m3u8_content set status = #status# where videoPoolId = #videoPoolId# and bitRateIndex = #bitRateIndex#
	</update>
		
	<select id="selectByVideoPoolIdAll" parameterClass="java.util.Map" resultClass="m3u8content">
		select * from m3u8_content where status != 8
	</select>
	
	<select id="selectList" parameterClass="java.util.Map" resultClass="m3u8content">
		select <include refid="_columns"/> from m3u8_content where status != 8

		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>

	<select id="selectListByVideoPoolId" parameterClass="string" resultClass="m3u8content">
		select * from m3u8_content where videoPoolId = #videoPoolId#
	</select>
	
	<select id="selectListCount" resultClass="int">
		select count(*) from m3u8_content where status != 8
	</select>

</sqlMap>
