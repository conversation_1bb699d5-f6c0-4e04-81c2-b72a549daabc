<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="playprofile">
	<typeAlias alias="playprofile" type="com.cc.ovp.domain.PlayProfile" />
	
	<sql id="_columns">
		userid,status,ext,autoid
	</sql>
	
	<insert id="insert" parameterClass="playprofile">
		insert into play_profile (userid, status, ext,autoid) values	(#userid#, #status#, #ext#, #autoid#)
	</insert>

	<update id="update" parameterClass="playprofile">
		update play_profile set status = #status#, ext = #ext# where userid = #userid#
	</update>
	
	<delete id="delete" parameterClass="long">
		delete from play_profile where autoid = #autoid#
	</delete>
	

	<select id="selectById" parameterClass="long" resultClass="playprofile">
		select * from play_profile where autoid = #autoid#
	</select>
	
	<select id="selectByUserid" parameterClass="String" resultClass="playprofile">
		select * from play_profile where userid = #userid#

	</select>
	
	<select id="selectList" resultClass="playprofile">
		select <include refid="_columns"/> from play_profile where 1=1 
		
		<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
		</isPropertyAvailable>
			
			
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount" resultClass="int">
		select count(*) from play_profile
	</select>	

</sqlMap>
