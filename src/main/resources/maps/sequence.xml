<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="sequence">
	<typeAlias alias="sequence" type="com.cc.ovp.domain.Sequence" />
	<!-- 根据表名选择记录 -->
	<select id="getByTable" parameterClass="java.lang.String" resultClass="sequence">
		select * from sequence where name = #tablename#
	</select>
	
	<!-- 第一次拿序列,创建序列记录 -->
	<insert id="create" parameterClass="sequence">
		insert into sequence (name,seed,lmodify) values (#name#,#seed#,#lmodify#)
	</insert>

	<!-- 序列自增,修改记录 -->
	<update id="update" parameterClass="sequence">
		update sequence set seed=#seed#,lmodify=#lmodify# where name=#name#
	</update>
</sqlMap>
