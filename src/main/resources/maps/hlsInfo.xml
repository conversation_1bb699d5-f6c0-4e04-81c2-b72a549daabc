<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="hlsInfo">
    <typeAlias alias="hlsInfo" type="com.cc.ovp.domain.HlsInfo" />

    <select id="selectOne" parameterClass="String" resultClass="hlsInfo">
        select videoPoolId,duration,tsFilesize1,tsFilesize2,tsFilesize3,tsCount1,tsCount2,tsCount3
        from hls_info 
        where videoPoolId = #videoPoolId#
     
    </select>
  
    
</sqlMap>
