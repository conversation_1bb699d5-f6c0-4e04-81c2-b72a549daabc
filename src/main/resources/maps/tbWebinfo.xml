<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tbWebinfo">

	<typeAlias alias="tbWebinfo" type="com.cc.ovp.domain.TbWebinfo" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	  <resultMap id="tbWebinfoResult" class="com.cc.ovp.domain.TbWebinfo">   
					<result property="id" column="id" nullValue="0"/> 
					<result property="flowpeice" column="flowpeice" nullValue="0"/> 
					<result property="spacepeice" column="spacepeice" nullValue="0"/> 

		  
		  </resultMap>
	
	<sql id="_columns">
							tb_webinfo.id,
							tb_webinfo.flowpeice,
							tb_webinfo.spacepeice
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ tb_webinfo.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="flowpeice"> 
									<isNotEmpty prepend="AND" property="flowpeice"> 
										<![CDATA[ tb_webinfo.flowpeice = #flowpeice# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="spacepeice"> 
									<isNotEmpty prepend="AND" property="spacepeice"> 
										<![CDATA[ tb_webinfo.spacepeice = #spacepeice# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="tbWebinfo">
		insert into tb_webinfo 
		(
							id,
							flowpeice,
							spacepeice
		)
		values
		(
							#id#,
							#flowpeice#,
							#spacepeice#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from tb_webinfo where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultMap="tbWebinfoResult">
		select <include refid="_columns"/> from tb_webinfo where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultMap="tbWebinfoResult">
		select <include refid="_columns"/> from tb_webinfo where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
		select <include refid="_columns"/> from tb_webinfo where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultMap="tbWebinfoResult">
		select <include refid="_columns"/> from tb_webinfo where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from tb_webinfo where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultMap="tbWebinfoResult">
		select <include refid="_columns"/> from tb_webinfo
	</select>
	
		<update id="update" parameterClass="tbWebinfo">
		update tb_webinfo set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="flowpeice"> 
							<isNotEmpty property="flowpeice"> 
								flowpeice = #flowpeice# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="spacepeice"> 
							<isNotEmpty property="spacepeice"> 
								spacepeice = #spacepeice# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

