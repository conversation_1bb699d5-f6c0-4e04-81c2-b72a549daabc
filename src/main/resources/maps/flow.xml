<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="flow">
	<typeAlias alias="flow" type="com.cc.ovp.domain.Flow" />
	<sql id="_columns">
		userid, indate, times, flowsize, ipnum, ip_avg_playtimes,playlong,moflowsize,moflowsize2,moflowsize3,dlflowsize,dtsflowsize,dpvflowsize
	</sql>
	
	<!-- 查询是否已经有记录 -->
	<select id="getByIndex" parameterClass="flow" resultClass="flow">
		select <include refid="_columns"/> from flow where  indate =#indate# and userid=#userid#
	</select>


	<!-- 更新记录 -->
	<update id="update" parameterClass="flow">
		update flow_stat set times=times+1 ,flowsize=#flowsize#,
		ipnum =#ipnum#,ip_avg_playtimes=#ip_avg_playtimes#
		where  indate =#indate# and userid=#userid#
	</update>
	
	
	
	
	
	<select id="selectList" parameterClass="java.util.Map" resultClass="flow">
		select <include refid="_columns"/>  from flow where 1=1
		  <isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										userid = #userid#
									</isNotEmpty>
		</isPropertyAvailable>
		
		<isPropertyAvailable property="startDate"> 
									<isNotEmpty prepend="AND" property="startDate"> 
										<![CDATA[datediff(indate,#startDate#)>=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		
		
		<isPropertyAvailable property="endDate"> 
									<isNotEmpty prepend="AND" property="endDate"> 
										<![CDATA[datediff(indate,#endDate#)<=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		 
		
		
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount"  parameterClass="java.util.Map" resultClass="int">
		select count(userid) from flow  where 1=1
		  <isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										userid = #userid#
									</isNotEmpty>
		</isPropertyAvailable>
		
		    	
		
		<isPropertyAvailable property="startDate"> 
									<isNotEmpty prepend="AND" property="startDate"> 
										<![CDATA[datediff(indate,#startDate#)>=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		
		
		<isPropertyAvailable property="endDate"> 
									<isNotEmpty prepend="AND" property="endDate"> 
										<![CDATA[datediff(indate,#endDate#)<=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		 
	</select>
	
		
	<select id="selectFlowTotal"  parameterClass="java.util.Map" resultClass="long">
		select sum(flowsize)+sum(moflowsize)+sum(moflowsize2)+sum(moflowsize3)+sum(dlflowsize) from flow  where 1=1
		  <isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										userid = #userid#
									</isNotEmpty>
		</isPropertyAvailable>
		
		    	
		
		<isPropertyAvailable property="startDate"> 
									<isNotEmpty prepend="AND" property="startDate"> 
										<![CDATA[datediff(indate,#startDate#)>=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		
		
		<isPropertyAvailable property="endDate"> 
									<isNotEmpty prepend="AND" property="endDate"> 
										<![CDATA[datediff(indate,#endDate#)<=0]]>
									</isNotEmpty>
		</isPropertyAvailable>
		 
	</select>


	<select id="selectFlowOfOneDay" parameterClass="java.util.Map" resultClass="long">
		SELECT sum(flowsize)+sum(moflowsize)+sum(dlflowsize)+sum(moflowsize2)+sum(moflowsize3)+sum(dlflowsize2)+sum(dtsflowsize)+sum(dpvflowsize)+sum(ovsflowsize)
		FROM flow
		WHERE userid = #userid#
		  AND indate = #date#
	</select>

	<select id="selectSumFlowOfOneDayBatch" parameterClass="java.util.Map" resultClass="com.cc.ovp.vo.UserResourceUsageChange">
		SELECT userid, sum(flowsize)+sum(moflowsize)+sum(dlflowsize)+sum(moflowsize2)+sum(moflowsize3)+sum(dlflowsize2)+sum(dtsflowsize)+sum(dpvflowsize)+sum(ovsflowsize) AS `change`
		FROM flow
		WHERE
		userid IN
		<iterate property="userIds" conjunction="," close=")" open="(" >
			#userIds[]#
		</iterate>
		AND indate = #date#
		GROUP BY userid
	</select>

</sqlMap>
