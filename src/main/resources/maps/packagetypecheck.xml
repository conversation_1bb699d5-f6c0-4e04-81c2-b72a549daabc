<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="packagetypecheck">

	<typeAlias alias="packagetypecheck" type="com.cc.AgentPlatform.domain.Packagetypecheck" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							id,
							userid,
							packageTypeid,
							agentPrice
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ packagetypecheck.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ packagetypecheck.userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="packageTypeid"> 
									<isNotEmpty prepend="AND" property="packageTypeid"> 
										<![CDATA[ packagetypecheck.packageTypeid = #packageTypeid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="packagetypecheck">
		insert into packagetypecheck 
		(
							id,
							userid,
							packageTypeid,
							agentPrice
		)
		values
		(
							#id#,
							#userid#,
							#packageTypeid#,
							#agentPrice#
		)
	</insert>

	<delete id="delete" parameterClass="String">
		delete from packagetypecheck where userid = #userid#
	</delete>
	
	<delete id="del" parameterClass="long">
		delete from packagetypecheck where id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="packagetypecheck">
		select <include refid="_columns"/> from packagetypecheck where 1=1 and
							id = #id#
	</select>
	
	<select id="getByUserId" parameterClass="String" resultClass="packagetypecheck">
		select <include refid="_columns"/> from packagetypecheck where 1=1 and
							packagetypecheck.userid = #userid#
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="packagetypecheck">
		select <include refid="_columns"/> from packagetypecheck where 1=1  
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="packagetypecheck">
		select <include refid="_columns"/> from packagetypecheck where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from packagetypecheck where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="packagetypecheck">
		select <include refid="_columns"/> from packagetypecheck
	</select>
	
		<update id="update" parameterClass="packagetypecheck">
		update packagetypecheck set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="packageTypeid"> 
							<isNotEmpty property="packageTypeid"> 
								packageTypeid = #packageTypeid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="agentPrice"> 
							<isNotEmpty property="agentPrice"> 
								agentPrice = #agentPrice# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

