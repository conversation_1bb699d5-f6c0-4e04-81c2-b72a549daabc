<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="packageTypeItem">

	<typeAlias alias="packageTypeItem" type="com.cc.AgentPlatform.domain.PackageTypeItem" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							packagetype_item.id,
							packagetype_item.packageType_id,
							packagetype_item.cc_user_item_id,
							packagetype_item.munber
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ packagetype_item.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="packageType_id"> 
									<isNotEmpty prepend="AND" property="packageType_id"> 
										<![CDATA[ packagetype_item.packageType_id = #packageType_id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="cc_user_item_id"> 
									<isNotEmpty prepend="AND" property="cc_user_item_id"> 
										<![CDATA[ packagetype_item.cc_user_item_id = #cc_user_item_id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="cc_user_item_ids"> 
									<isNotEmpty prepend="AND" property="cc_user_item_ids"> 
									    	packagetype_item.cc_user_item_id in
										<iterate  prepend="" property="cc_user_item_ids"  open="("  close=")"  conjunction="," >
										               #cc_user_item_ids[]#
										</iterate>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="packageTypeItem">
		insert into packagetype_item 
		(
							id,
							packageType_id,
							cc_user_item_id,
							munber
		)
		values
		(
							#id#,
							#packageType_id#,
							#cc_user_item_id#,
							#munber#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from packagetype_item where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="packageTypeItem">
		select <include refid="_columns"/> from packagetype_item where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="packageTypeItem">
		select <include refid="_columns"/> from packagetype_item where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
		select <include refid="_columns"/> from packagetype_item where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="packageTypeItem">
		select <include refid="_columns"/> from packagetype_item where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from packagetype_item where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="packageTypeItem">
		select <include refid="_columns"/> from packagetype_item
	</select>
	
		<update id="update" parameterClass="packageTypeItem">
		update packagetype_item set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="packageType_id"> 
							<isNotEmpty property="packageType_id"> 
								packageType_id = #packageType_id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="cc_user_item_id"> 
							<isNotEmpty property="cc_user_item_id"> 
								cc_user_item_id = #cc_user_item_id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="munber"> 
							<isNotEmpty property="munber"> 
								munber = #munber# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

