<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="ccUser">

    <typeAlias alias="ccUser" type="com.cc.usercenter.domain.CcUser" />
    <typeAlias alias="doc" type="ayou.util.DOC" />

      <resultMap id="ccUserResult" class="com.cc.usercenter.domain.CcUser">
          <result property="autoid" column="autoid"/>
          <result property="email" column="email"/>
          <result property="note" column="note"/>
          <result property="userid" column="userid"/>
          <result property="company" column="company"/>
          <result property="password" column="password"/>
          <result property="grade" column="grade"/>
          <result property="createuser" column="createuser"/>
          <result property="ptime" column="ptime" nullValue="2012/04/13 00:00:00"/>
          <result property="lmodify" column="lmodify" nullValue="2012/04/13 00:00:00"/>
          <result property="status" column="status" nullValue="1"/>
          <result property="lasttime" column="lasttime" nullValue="2012/04/13 00:00:00"/>
          <result property="webname" column="webname"/>
          <result property="weburl" column="weburl"/>
          <result property="province" column="province"/>
          <result property="city" column="city"/>
          <result property="name" column="name"/>
          <result property="qq" column="qq"/>
          <result property="validtime" column="validtime" nullValue="2012/04/13 00:00:00"/>
          <result property="telephone" column="telephone"/>
          <result property="officetel" column="officetel"/>
          <result property="field1" column="field1"/>
          <result property="field2" column="field2"/>
          <result property="space" column="space"/>
          <result property="writetoken" column="writetoken"/>
          <result property="readtoken" column="readtoken"/>
          <result property="secretkey" column="secretkey"/>
          <result property="autoedit" column="autoedit" nullValue="0"/>
          <result property="parent_userid" column="parent_userid" nullValue=""/>
          <result property="del" column="del" nullValue="0"/>
          <result property="flowsize" column="flowsize" />
          <result property="expirationtime" column="expirationtime"/>
          <result property="balance" column="balance"   nullValue="0"/>
          <result property="usedflow" column="usedflow" nullValue="0"/>
          <result property="usedspace" column="usedspace" nullValue="0"/>
          <result property="playurl" column="playurl"/>
          <result property="cdnurl" column="cdnurl"/>
          <result property="cdnurl2" column="cdnurl2"/>
          <result property="cdnurl3" column="cdnurl3"/>
          <result property="segurl" column="segurl"/>
          <result property="segurl2" column="segurl2"/>

          <result property="ext" column="ext"/>
          <result property="packagetypeid" column="packagetypeid"/>
          <result property="tcurl" column="transcoding_callback_url"/>
          <result property="sign" column="sign" nullValue="0"/>
          <result property="period" column="period" nullValue="1"/>
          <result property="applevel" column="applevel" />
          <result property="weblevel" column="weblevel" />
          <result property="umsenabled" column="user_msg_sei_enabled" nullValue="N" />
          <result property="forbidClients" column="forbidClients" nullValue="" />
          <result property="engagementEnabled" column="engagement_enabled" />
          </resultMap>

    <resultMap id="ccUserResult2" class="com.cc.usercenter.domain.CcUser">
          <result property="autoid" column="autoid"/>
          <result property="email" column="email"/>
          <result property="note" column="note"/>
          <result property="userid" column="userid"/>
          <result property="company" column="company"/>
          <result property="password" column="password"/>
          <result property="grade" column="grade"/>
          <result property="createuser" column="createuser"/>
          <result property="ptime" column="ptime" nullValue="2012/04/13 00:00:00"/>
          <result property="lmodify" column="lmodify" nullValue="2012/04/13 00:00:00"/>
          <result property="status" column="status" nullValue="1"/>
          <result property="lasttime" column="lasttime" nullValue="2012/04/13 00:00:00"/>
          <result property="webname" column="webname"/>
          <result property="weburl" column="weburl"/>
          <result property="province" column="province"/>
          <result property="city" column="city"/>
          <result property="name" column="name"/>
          <result property="qq" column="qq"/>
          <result property="validtime" column="validtime" nullValue="2012/04/13 00:00:00"/>
          <result property="telephone" column="telephone"/>
          <result property="officetel" column="officetel"/>
          <result property="field1" column="field1"/>
          <result property="field2" column="field2"/>
          <result property="space" column="space"/>
          <result property="writetoken" column="writetoken"/>
          <result property="readtoken" column="readtoken"/>
          <result property="secretkey" column="secretkey"/>
          <result property="autoedit" column="autoedit" nullValue="0"/>
          <result property="parent_userid" column="parent_userid" nullValue=""/>
          <result property="del" column="del" nullValue="0"/>
          <result property="flowsize" column="flowsize" />
          <result property="expirationtime" column="expirationtime"/>
          <result property="balance" column="balance"   nullValue="0"/>
          <result property="usedflow" column="usedflow" nullValue="0"/>
          <result property="usedspace" column="usedspace" nullValue="0"/>
          <result property="playurl" column="playurl"/>
          <result property="cdnurl" column="cdnurl"/>
          <result property="cdnurl2" column="cdnurl2"/>
          <result property="cdnurl3" column="cdnurl3"/>
          <result property="segurl" column="segurl"/>
          <result property="segurl2" column="segurl2"/>

          <result property="ext" column="ext"/>
          <result property="packagetypeid" column="packagetypeid"/>
          <result property="tcurl" column="transcoding_callback_url"/>
          <result property="sign" column="sign" nullValue="0"/>
          <result property="period" column="period" nullValue="1"/>
          <result property="inviter" column="inviter" nullValue=""/>
          <result property="applevel" column="applevel" />
          <result property="weblevel" column="weblevel" />
          <result property="umsenabled" column="user_msg_sei_enabled" nullValue="N" />
          <result property="forbidClients" column="forbidClients" nullValue="" />
          <result property="pcH5Domain" column="pc_h5_domain" />
          <result property="engagementEnabled" column="engagement_enabled" />
    </resultMap>




    <sql id="_columns">
                            a.autoid,
                            a.email,
                            a.note,
                            a.userid,
                            a.company,
                            a.password,
                            a.grade,
                            a.createuser,
                            a.ptime,
                            a.lmodify,
                            a.status,
                            a.lasttime,
                            a.webname,
                            a.weburl,
                            a.province,
                            a.city,
                            a.name,
                            a.qq,
                            a.validtime,
                            a.telephone,
                            a.officetel,
                            a.field1,
                            a.field2,
                            t.space,
                            a.writetoken,
                            a.readtoken,
                            a.secretkey,
                            a.autoedit,
                            a.parent_userid,
                            a.del,
                            if(t.flow is null,0,t.flow) as flowsize,
                            t.expirationtime,
                            t.cmoney as balance,
                            a.usedflow,
                            a.usedspace,
                            a.playurl,
                            a.cdnurl,
                            a.ext,
                            a.packagetypeid, a.transcoding_callback_url,a.cdnurl2,a.cdnurl3,a.segurl,a.segurl2,a.sign,a.period,
                            a.applevel, a.weblevel, a.user_msg_sei_enabled, a.forbidClients,
                            a.pc_h5_domain,
                            a.engagement_enabled
    </sql>

    <sql id="_condition">
                                <isPropertyAvailable property="tcurl">
                                    <isNotEmpty prepend="AND" property="tcurl">
                                        <![CDATA[ a.transcoding_callback_url = #tcurl# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                                <isPropertyAvailable property="playurl">
                                    <isNotEmpty prepend="AND" property="playurl">
                                        <![CDATA[ a.playurl = #playurl# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                                <isPropertyAvailable property="cdnurl">
                                    <isNotEmpty prepend="AND" property="cdnurl">
                                        <![CDATA[ a.cdnurl = #cdnurl# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                                <isPropertyAvailable property="segurl">
                                    <isNotEmpty prepend="AND" property="segurl">
                                        <![CDATA[ a.segurl = #segurl# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                               <isPropertyAvailable property="segurl2">
                                    <isNotEmpty prepend="AND" property="segurl2">
                                        <![CDATA[ a.segurl2 = #segurl2# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                                <isPropertyAvailable property="usedflow">
                                    <isNotEmpty prepend="AND" property="usedflow">
                                        <![CDATA[ a.usedflow = #usedflow# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                                <isPropertyAvailable property="usedspace">
                                    <isNotEmpty prepend="AND" property="usedspace">
                                        <![CDATA[ a.usedspace = #usedspace# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                                <isPropertyAvailable property="flowsize">
                                    <isNotEmpty prepend="AND" property="flowsize">
                                        <![CDATA[ a.flowsize = #flowsize# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                                <isPropertyAvailable property="expirationtime">
                                    <isNotEmpty prepend="AND" property="expirationtime">
                                        <![CDATA[ a.expirationtime = #expirationtime# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>
                                <isPropertyAvailable property="balance">
                                    <isNotEmpty prepend="AND" property="balance">
                                        <![CDATA[ a.balance = #balance# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>

                                <isPropertyAvailable property="autoid">
                                    <isNotEmpty prepend="AND" property="autoid">
                                        <![CDATA[ a.autoid = #autoid# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="email">
                                    <isNotEmpty prepend="AND" property="email">
                                        <![CDATA[ a.email = #email# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>

                                <isPropertyAvailable property="emaillike">
                                    <isNotEmpty prepend="AND" property="emaillike">
                                        <![CDATA[ a.email like '%$emaillike$%']]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="userid">
                                    <isNotEmpty prepend="AND" property="userid">
                                        <![CDATA[ a.userid = #userid# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>

                                <isPropertyAvailable property="packagetypeid">
                                    <isNotEmpty prepend="AND" property="packagetypeid">
                                        <![CDATA[ a.packagetypeid = #packagetypeid# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="company">
                                    <isNotEmpty prepend="AND" property="company">
                                        <![CDATA[ a.company = #company# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="password">
                                    <isNotEmpty prepend="AND" property="password">
                                        <![CDATA[ a.password = #password# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="grade">
                                    <isNotEmpty prepend="AND" property="grade">
                                        <![CDATA[ a.grade = #grade# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="createuser">
                                    <isNotEmpty prepend="AND" property="createuser">
                                        <![CDATA[ a.createuser = #createuser# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="ptime">
                                    <isNotEmpty prepend="AND" property="ptime">
                                        <![CDATA[a.ptime<DATE_FORMAT( '$ptime$', '%Y-%m-%d' ) )]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="lmodify">
                                    <isNotEmpty prepend="AND" property="lmodify">
                                        <![CDATA[a.lmodify<DATE_FORMAT( '$lmodify$', '%Y-%m-%d' ) )]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="status">
                                    <isNotEmpty prepend="AND" property="status">
                                        <![CDATA[ a.status = #status# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="lasttime">
                                    <isNotEmpty prepend="AND" property="lasttime">
                                        <![CDATA[a.lasttime<DATE_FORMAT( '$lasttime$', '%Y-%m-%d' ) )]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="webname">
                                    <isNotEmpty prepend="AND" property="webname">
                                        <![CDATA[ a.webname = #webname# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>

                                <isPropertyAvailable property="weburllike">
                                    <isNotEmpty prepend="AND" property="weburllike">
                                        <![CDATA[ a.weburl like '%$weburllike$%']]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="weburl">
                                    <isNotEmpty prepend="AND" property="weburl">
                                        <![CDATA[ a.weburl = #weburl# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="province">
                                    <isNotEmpty prepend="AND" property="province">
                                        <![CDATA[ a.province = #province# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="city">
                                    <isNotEmpty prepend="AND" property="city">
                                        <![CDATA[ a.city = #city# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="name">
                                    <isNotEmpty prepend="AND" property="name">
                                        <![CDATA[ a.name = #name# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="qq">
                                    <isNotEmpty prepend="AND" property="qq">
                                        <![CDATA[ a.qq = #qq# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="validtime">
                                    <isNotEmpty prepend="AND" property="validtime">
                                        <![CDATA[a.validtime<DATE_FORMAT( '$validtime$', '%Y-%m-%d' ) )]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="telephone">
                                    <isNotEmpty prepend="AND" property="telephone">
                                        <![CDATA[ a.telephone = #telephone# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="officetel">
                                    <isNotEmpty prepend="AND" property="officetel">
                                        <![CDATA[ a.officetel = #officetel# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="field1">
                                    <isNotEmpty prepend="AND" property="field1">
                                        <![CDATA[ a.field1 = #field1# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="field2">
                                    <isNotEmpty prepend="AND" property="field2">
                                        <![CDATA[ a.field2 = #field2# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="space">
                                    <isNotEmpty prepend="AND" property="space">
                                        <![CDATA[ a.space = #space# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="writetoken">
                                    <isNotEmpty prepend="AND" property="writetoken">
                                        <![CDATA[ a.writetoken = #writetoken# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="readtoken">
                                    <isNotEmpty prepend="AND" property="readtoken">
                                        <![CDATA[ a.readtoken = #readtoken# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>

                                <isPropertyAvailable property="secretkey">
                                    <isNotEmpty prepend="AND" property="secretkey">
                                        <![CDATA[ a.secretkey = #secretkey# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="autoedit">
                                    <isNotEmpty prepend="AND" property="autoedit">
                                        <![CDATA[ a.autoedit = #autoedit# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="sign">
                                    <isNotEmpty prepend="AND" property="sign">
                                        <![CDATA[ a.sign = #sign# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>

                                <isPropertyAvailable property="parent_userid">
                                    <isNotEmpty prepend="AND" property="parent_userid">
                                        <![CDATA[ a.parent_userid = #parent_userid# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>


                                <isPropertyAvailable property="del">
                                    <isNotEmpty prepend="AND" property="del">
                                        <![CDATA[ a.del = #del# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>

                                <isPropertyAvailable property="userids">
                                    <isNotEmpty prepend="AND" property="userids">
                                            a.userid in
                                            <iterate property="userids" open="(" close=")" conjunction=",">
                                              #userids[]#
                                            </iterate>
                                    </isNotEmpty>
                                </isPropertyAvailable>

                                <isPropertyAvailable property="period">
                                    <isNotEmpty prepend="AND" property="period">
                                        <![CDATA[ a.period = #period# ]]>
                                    </isNotEmpty>
                                </isPropertyAvailable>

    </sql>

    <insert id="insert" parameterClass="ccUser">
        insert into cc_user
        (
                            email,
                            userid,
                            note,
                            company,
                            password,
                            grade,
                            createuser,
                            ptime,
                            lmodify,
                            status,
                            lasttime,
                            webname,
                            weburl,
                            province,
                            city,
                            name,
                            qq,
                            validtime,
                            telephone,
                            officetel,
                            field1,
                            field2,
                            writetoken,
                            readtoken,
                            secretkey,
                            autoedit,
                            parent_userid,
                            del,
                            balance,
                            usedspace,
                            usedflow,
                            playurl,
                            ext,
                            packagetypeid,
                            sign,
                            transcoding_callback_url
        )
        values
        (
                            #email#,
                            #userid#,
                            #note#,
                            #company#,
                            #password#,
                            #grade#,
                            #createuser#,
                            #ptime#,
                            #lmodify#,
                            #status#,
                            #lasttime#,
                            #webname#,
                            #weburl#,
                            #province#,
                            #city#,
                            #name#,
                            #qq#,
                            #validtime#,
                            #telephone#,
                            #officetel#,
                            #field1#,
                            #field2#,
                            #writetoken#,
                            #readtoken#,
                            #secretkey#,
                            #autoedit#,
                            #parent_userid#,
                            #del#,

                            #balance#,
                            #usedspace#,
                            #usedflow#,
                            #playurl#,
                            #ext#,
                            #packagetypeid#,
                            #sign#,
                            #tcurl#
        )
        <selectKey resultClass="long" keyProperty="autoid" type="post">
             <![CDATA[SELECT LAST_INSERT_ID() AS ID ]]>
           </selectKey>
    </insert>

    <delete id="delete" parameterClass="long">
        update cc_user set del=1 where autoid = #autoid#
    </delete>

    <select id="getById" parameterClass="long" resultMap="ccUserResult">
        select <include refid="_columns"/>from cc_user as a left join available_traffic as t on a.userid=t.userid where autoid = #autoid#
    </select>

    <select id="selectList" parameterClass="doc" resultMap="ccUserResult2">
        select <include refid="_columns"/>,u.inviter from cc_user as a,available_traffic t,union_user u where a.del=0 and a.userid=t.userid and u.vod_userid=a.userid


          <isPropertyAvailable property="userid">
                                    <isNotEmpty prepend="AND" property="userid">
                                        t.userid = #userid#
                                    </isNotEmpty>
        </isPropertyAvailable>
        <include refid="_condition"/>

        <dynamic prepend="ORDER BY">
           <isNotEmpty property="orderstr">
         $orderstr$ $sortDirection$
           </isNotEmpty>
          </dynamic>

        <dynamic prepend="limit">
            <isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
            <isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
        </dynamic>
    </select>

    <select id="selectListMap" parameterClass="java.util.Map" resultClass="ccUser">
        select <include refid="_columns"/> from cc_user as a,available_traffic t where a.del=0 and a.userid=t.userid
            <include refid="_condition"/>
          <dynamic prepend="ORDER BY">
           <isNotEmpty property="orderstr">
            $orderstr$ $sortDirection$
           </isNotEmpty>
          </dynamic>

        <dynamic prepend="limit">
            <isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
            <isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
        </dynamic>
    </select>

    <select id="selectByDelList" parameterClass="doc" resultClass="ccUser">
        select <include refid="_columns"/> from cc_user as a,available_traffic t where a.del=1 and a.userid=t.userid
        <include refid="_condition"/>

        <dynamic prepend="ORDER BY">
           <isNotEmpty property="orderstr">
         $orderstr$ $sortDirection$
           </isNotEmpty>
          </dynamic>

        <dynamic prepend="limit">
            <isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
            <isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
        </dynamic>
    </select>

    <select id="selectByDelListMap" parameterClass="java.util.Map" resultClass="ccUser">
        select <include refid="_columns"/> from cc_user as a,available_traffic t where a.del=1 and a.userid=t.userid
            <include refid="_condition"/>
          <dynamic prepend="ORDER BY">
           <isNotEmpty property="orderstr">
            $orderstr$ $sortDirection$
           </isNotEmpty>
          </dynamic>

        <dynamic prepend="limit">
            <isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
            <isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
        </dynamic>
    </select>

        <select id="getByDelCount" resultClass="int">
        select count(*) from cc_user as a,available_traffic t where a.del=1 and a.userid=t.userid
            <include refid="_condition"/>
    </select>

    <select id="getByUseridList" resultClass="String">
        select a.userid from cc_user as a,available_traffic t where a.del=0 and a.userid=t.userid
            <include refid="_condition"/>
    </select>

    <select id="getNotDeleteUserList" resultClass="ccUser">
        select  <include refid="_columns"/> from cc_user as a,available_traffic t where a.del=0 and a.userid=t.userid
            <include refid="_condition"/>
    </select>

    <select id="getCount" resultClass="int">
        select count(*) from cc_user as a,available_traffic t where a.del=0  and a.userid=t.userid
            <include refid="_condition"/>
    </select>

    <select id="all"  resultClass="ccUser">
        select <include refid="_columns"/> from cc_user as a,available_traffic t where a.userid=t.userid
    </select>

        <update id="update" parameterClass="ccUser">
        update cc_user set
                        <isPropertyAvailable property="tcurl">
                            <isNotEmpty property="tcurl">
                                transcoding_callback_url = #tcurl# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="playurl">
                            <isNotEmpty property="playurl">
                                playurl = #playurl# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="usedspace">
                            <isNotEmpty property="usedspace">
                                usedspace = #usedspace# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="usedflow">
                            <isNotEmpty property="usedflow">
                                usedflow = #usedflow# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="autoid">
                            <isNotEmpty property="autoid">
                                autoid = #autoid# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="cdnurl">
                            <isNotEmpty property="cdnurl">
                                cdnurl = #cdnurl# ,
                            </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="cdnurl2">
                            <isNotEmpty property="cdnurl2">
                                cdnurl2 = #cdnurl2# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="segurl">
                            <isNotEmpty property="segurl">
                                segurl = #segurl# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="segurl2">
                            <isNotEmpty property="segurl2">
                                segurl2 = #segurl2# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="email">
                            <isNotEmpty property="email">
                                email = #email# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="note">
                            <isNotEmpty property="note">
                                note = #note# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="userid">
                            <isNotEmpty property="userid">
                                userid = #userid# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="company">
                            <isNotEmpty property="company">
                                company = #company# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="password">
                            <isNotEmpty property="password">
                                password = #password# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="grade">
                            <isNotEmpty property="grade">
                                grade = #grade# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="createuser">
                            <isNotEmpty property="createuser">
                                createuser = #createuser# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="ptime">
                            <isNotEmpty property="ptime">
                                ptime = #ptime# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="lmodify">
                            <isNotEmpty property="lmodify">
                                lmodify = #lmodify# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="status">
                            <isNotEmpty property="status">
                                status = #status# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="lasttime">
                            <isNotEmpty property="lasttime">
                                lasttime = #lasttime# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="webname">
                            <isNotEmpty property="webname">
                                webname = #webname# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="weburl">
                            <isNotEmpty property="weburl">
                                weburl = #weburl# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="province">
                            <isNotEmpty property="province">
                                province = #province# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="city">
                            <isNotEmpty property="city">
                                city = #city# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="name">
                            <isNotEmpty property="name">
                                name = #name# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="qq">
                            <isNotEmpty property="qq">
                                qq = #qq# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="validtime">
                            <isNotEmpty property="validtime">
                                validtime = #validtime# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="telephone">
                            <isNotEmpty property="telephone">
                                telephone = #telephone# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="officetel">
                            <isNotEmpty property="officetel">
                                officetel = #officetel# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="field1">
                            <isNotEmpty property="field1">
                                field1 = #field1# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="field2">
                            <isNotEmpty property="field2">
                                field2 = #field2# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="writetoken">
                            <isNotEmpty property="writetoken">
                                writetoken = #writetoken# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="readtoken">
                            <isNotEmpty property="readtoken">
                                readtoken = #readtoken# ,
                            </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="secretkey">
                            <isNotEmpty property="secretkey">
                                secretkey = #secretkey# ,
                            </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="autoedit">
                            <isNotEmpty property="autoedit">
                                autoedit = #autoedit# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="parent_userid">
                            <isNotEmpty property="parent_userid">
                                parent_userid = #parent_userid# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="sign">
                            <isNotEmpty property="sign">
                                sign = #sign# ,
                            </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="del">
                            <isNotEmpty property="del">
                                del = #del# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                        <isPropertyAvailable property="balance">
                            <isNotEmpty property="balance">
                                balance = #balance#,
                            </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="period">
                            <isNotEmpty property="period">
                                period = #period# ,
                            </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="weblevel">
                          <isNotEmpty property="weblevel">
                            weblevel = #weblevel# ,
                          </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="applevel">
                          <isNotEmpty property="applevel">
                            applevel = #applevel# ,
                          </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="umsenabled">
                          <isNotEmpty property="umsenabled">
                            user_msg_sei_enabled = #umsenabled# ,
                          </isNotEmpty>
                        </isPropertyAvailable>

                        <isPropertyAvailable property="packagetypeid">
                            <isNotEmpty property="packagetypeid">
                                packagetypeid = #packagetypeid# ,
                            </isNotEmpty>
                        </isPropertyAvailable>
                            autoid = #autoid#
        where
                            autoid = #autoid#
    </update>

    <update id="del" parameterClass="String">
        delete from cc_user where userid = #userid#
    </update>

        <update id="upext" parameterClass="java.util.Map">
        update  cc_user set ext = #ext# where userid = #userid#
    </update>

    <select id="selectUserStates" parameterClass="java.util.Map" resultClass="com.cc.ovp.vo.UserState">
        SELECT u.userid AS userId, u.usedspace AS usedSpace, u.usedflow AS usedFlow, uu.unionid AS unionId,
        a.expirationtime AS expirationTime, IF(a.space IS NULL, 0, a.space) AS totalSpace, IF(a.flow IS NULL, 0, a.flow) AS totalFlow
        FROM cc_user u
        LEFT JOIN union_user AS uu ON u.userid = uu.vod_userid
        LEFT JOIN available_traffic AS a ON u.userid = a.userid
        WHERE u.userid IN
        <iterate property="userIds" conjunction="," close=")" open="(" >
            #userIds[]#
        </iterate>
    </select>

</sqlMap>

