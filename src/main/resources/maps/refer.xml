<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="refer">
	<typeAlias alias="refer" type="com.cc.ovp.domain.Refer" />
	<!-- 插入记录 -->
	<insert id="insert" parameterClass="refer">
		insert into refer(userid,indate,refer,times,videoid,ip,province)
		values(
		#userid#,#indate#,#refer#,#times#,#videoid#,#ip#,#province#
		)
	</insert>
	<!-- 获得一行记录 -->
		<select id="selectRefer" parameterClass="refer" resultClass="refer">
		select * from refer where  
		videoid=#videoid#  and refer=#refer# and indate=#indate# and ip =#ip#
	   </select>
	   <select id="selectOne" parameterClass="refer" resultClass="refer">
		select * from refer where  
		videoid=#videoid#  and refer=#refer# and indate=#indate# 
	   </select>
	   
	<!-- 计算条数 -->
	<select id="selectCount"  parameterClass="java.util.Map" resultClass="int">
		<![CDATA[select count(*)  from refer 
		where datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0 and videoid=#videoid#]]>
	   </select>
	   
	<update id="update" parameterClass="refer">
		update  refer 
		set userid=#userid#,
		indate=#indate#,refer=#refer#,times=#times#
		where videoid=#videoid# and refer=#refer# and indate=#indate# and ip=#ip#
	</update>


	<!-- 获得两个日期间的数据记录-->
	<select  id="selectList" parameterClass="java.util.Map" resultClass="refer">
	<![CDATA[select *  from refer 
		where datediff(indate,#startDate#)>=0 and datediff(indate,#endDate#)<=0 and videoid=#videoid# ]]>
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
	</select>
</sqlMap>
