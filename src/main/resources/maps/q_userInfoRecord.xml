<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="quserRecord">
    <typeAlias alias="quserInfoRecord" type="com.cc.ovp.domain.QuserInfoRecord" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

  <sql id="_columns">
		qid, userid, sourcetitle, ip, ipposition, pagelink, ext, date, userhref
  </sql>

	<insert id="insert" parameterClass="quserInfoRecord">
	<![CDATA[
		insert into q_userInfoRecord
		(qid, userid, sourcetitle, ip, ipposition, pagelink, ext, date, userhref, vid)
		values
		(#qid#, #userid#, #sourcetitle#, #ip#, #ipPosition#, #pagelink#, #ext#, #date#, #userhref#, #vid#)
     ]]>
	</insert>
	
	<select id="selectInfoByqid" parameterClass="java.lang.String" resultClass="quserInfoRecord">
	    select * from q_userInfoRecord where qid=#qid#
	</select>
	
	<delete id="deleteInfoByqid" parameterClass="java.lang.String">
	    delete from q_userInfoRecord where qid=#qid#
	</delete>
	   
	 <select id="selectList" parameterClass="java.util.Map" resultClass="quserInfoRecord">
		select * from q_userInfoRecord  where  qid=#qid#
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount" parameterClass="java.util.Map" resultClass="int">
		select count(*) from q_userInfoRecord where  qid=#qid#
	</select>
</sqlMap>