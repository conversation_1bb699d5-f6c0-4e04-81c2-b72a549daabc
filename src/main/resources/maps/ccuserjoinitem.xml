<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="ccuserjoinitem">

	<typeAlias alias="ccuserjoinitem" type="com.cc.usercenter.domain.Ccuserjoinitem" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							ccuserjoinitem.userid,
							ccuserjoinitem.cc_user_item_id,
							ccuserjoinitem.addtime,
							ccuserjoinitem.endtime,
							ccuserjoinitem.id,
							ccuserjoinitem.munber
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ ccuserjoinitem.userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="cc_user_item_id"> 
									<isNotEmpty prepend="AND" property="cc_user_item_id"> 
										<![CDATA[ ccuserjoinitem.cc_user_item_id = #cc_user_item_id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="cc_user_item_ids"> 
									<isNotEmpty prepend="AND" property="cc_user_item_ids"> 
									    ccuserjoinitem.cc_user_item_id in
									    <iterate property="cc_user_item_ids" conjunction="," close=")" open="(" >
										#cc_user_item_ids[]#
										</iterate>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="addtime"> 
									<isNotEmpty prepend="AND" property="addtime"> 
										<![CDATA[ccuserjoinitem.addtime<DATE_FORMAT( '$addtime$', '%Y-%m-%d' ) ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="endtime"> 
									<isNotEmpty prepend="AND" property="endtime"> 
										<![CDATA[ccuserjoinitem.endtime<DATE_FORMAT( '$endtime$', '%Y-%m-%d' ) ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="bendtime"> 
									<isNotEmpty prepend="AND" property="bendtime"> 
										<![CDATA[ccuserjoinitem.endtime>DATE_FORMAT( '$bendtime$', '%Y-%m-%d' ) ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ ccuserjoinitem.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="ccuserjoinitem">
		insert into ccuserjoinitem (userid,cc_user_item_id,addtime,endtime,munber)values(#userid#,#cc_user_item_id#,#addtime#,#endtime#,#munber#)
		<selectKey resultClass="long" keyProperty="id" type="post">   
             <![CDATA[SELECT LAST_INSERT_ID() AS ID ]]>  
           </selectKey>  
	</insert>

	<delete id="delete" parameterClass="long">
		delete from ccuserjoinitem where 1=1 and
							id = #id#
	</delete>
	<delete id="del" parameterClass="String">
		delete from ccuserjoinitem where 1=1 and userid = #userid#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="ccuserjoinitem">
		select <include refid="_columns"/> from ccuserjoinitem where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="ccuserjoinitem">
		select <include refid="_columns"/> from ccuserjoinitem where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
SELECT ccuserjoinitem.id as cid,ccuserjoinitem.endtime,ccuserjoinitem.userid,ccuserjoinitem.munber,ccuserjoinitem.addtime,cc_user_item.item,cc_user_item.itemurl,cc_user_item.txt,cc_user_item.id from ccuserjoinitem right join cc_user_item on ccuserjoinitem.cc_user_item_id=cc_user_item.id where ccuserjoinitem.userid=#userid# and ccuserjoinitem.endtime > SUBTIME(now(), '00:01')
	</select>

	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="ccuserjoinitem">
		select <include refid="_columns"/> from ccuserjoinitem where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from ccuserjoinitem where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="ccuserjoinitem">
		select <include refid="_columns"/> from ccuserjoinitem
	</select>
	
		<update id="update" parameterClass="ccuserjoinitem">
		update ccuserjoinitem set
		
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="cc_user_item_id"> 
							<isNotEmpty property="cc_user_item_id"> 
								cc_user_item_id = #cc_user_item_id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="addtime"> 
							<isNotEmpty property="addtime"> 
								addtime = #addtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="endtime"> 
							<isNotEmpty property="endtime"> 
								endtime = #endtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="munber"> 
							<isNotEmpty property="munber"> 
								munber = #munber# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

