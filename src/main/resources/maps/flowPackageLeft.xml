<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="flowPackageLeft">
    <typeAlias alias="flowPackageLeft" type="com.cc.AgentPlatform.domain.FlowPackageLeft" />
    
    <sql id="_columns">
        userId,leftFlow,createTime
    </sql>
    
    <insert id="insert" parameterClass="flowPackageLeft">
        insert into flow_package_left (userId,leftFlow,createtime) values (#userId#,#leftFlow#,#createTime#)
    </insert>
    
    <insert id="update" parameterClass="flowPackageLeft">
        update flow_package_left set userId = #userId#,createTime = #createTime# ,leftFlow = #leftFlow# where userId = #userId# and createTime = #createTime#
    </insert>
    
    <insert id="updateByUserId">
        update flow_package_left set leftFlow = leftFlow + #flow# where userId = #userId#
    </insert>         
    
    <delete id="delete" parameterClass="String">
        delete from flow_package_left where userId = #userId#
    </delete>   
    
    <select id="selectList" resultClass="flowPackageLeft">
        select <include refid="_columns"/> from flow_package_left where 1=1 
        
        <isPropertyAvailable property="userId"> 
                <isNotEmpty prepend="AND" property="userId"> 
                    <![CDATA[userId = #userId# ]]>
                </isNotEmpty>
        </isPropertyAvailable>
        
        <isPropertyAvailable property="createTime"> 
                <isNotEmpty prepend="AND" property="createTime"> 
                    <![CDATA[datediff(createTime,#createTime#)<=0 ]]>
                </isNotEmpty>
        </isPropertyAvailable>
        
        <dynamic prepend="ORDER BY">  
           <isNotEmpty property="orderstr">  
            $orderstr$ $sortDirection$
           </isNotEmpty>  
        </dynamic>                                                 
            
        <dynamic prepend="limit">
            <isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
            <isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
        </dynamic>
    </select>
    
    <select id="selectListLess" resultClass="flowPackageLeft">
        select <include refid="_columns"/> from flow_package_left where 1=1 
        
        <isPropertyAvailable property="userId"> 
                <isNotEmpty prepend="AND" property="userId"> 
                    <![CDATA[userId = #userId# ]]>
                </isNotEmpty>
        </isPropertyAvailable>
        
        <isPropertyAvailable property="createTime"> 
                <isNotEmpty prepend="AND" property="createTime"> 
                    <![CDATA[datediff(createTime,#createTime#)>0 ]]>
                </isNotEmpty>
        </isPropertyAvailable>
        
        <dynamic prepend="ORDER BY">  
           <isNotEmpty property="orderstr">  
            $orderstr$ $sortDirection$
           </isNotEmpty>  
        </dynamic>                                                 
            
        <dynamic prepend="limit">
            <isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
            <isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
        </dynamic>
    </select>    
</sqlMap>