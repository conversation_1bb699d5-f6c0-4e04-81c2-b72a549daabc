<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="video_pool_all">
	<typeAlias alias="review" type="com.cc.ovp.domain.Review" />
<select id="selectList" resultClass="review">
		select video_pool_id, userid, title, ext,ptime,adminid from video_pool_all
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount" resultClass="int">
		select count(*) from video_pool_all
	</select>
	
	<update id="updateStatus" parameterClass="review">
		update video_pool_all set status=#status#,adminid=#adminid#
		where video_pool_id=#video_pool_id#,userid=#userid#
	</update>
</sqlMap>
