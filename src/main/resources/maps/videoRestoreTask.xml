<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
        PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
        "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="videoRestoreTask">

    <resultMap id="videoRestoreTask" class="net.polyv.domain.VideoRestoreTask">
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="video_pool_id" property="videoPoolId" />
        <result column="expire_seconds" property="expireSeconds" />
        <result column="url" property="url" />
        <result column="task_id" property="taskId" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="success_time" property="successTime" />
    </resultMap>

    <select id="selectByPrimaryKey" parameterClass="java.util.Map" resultMap="videoRestoreTask">
        select `id`, `task_id`, `video_pool_id`, `expire_seconds`, `url`, `status`, `create_time`, `success_time`
        from video_restore_task where id = #id#
    </select>

</sqlMap>