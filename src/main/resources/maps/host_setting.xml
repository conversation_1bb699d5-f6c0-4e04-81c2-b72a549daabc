<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="host_setting">
	<typeAlias alias="host_setting" type="com.cc.ovp.domain.Host_setting" />
	
	 
	<sql id="_columns">
		userid, enable_host, disable_host, setting_type
	</sql>
	<!-- 添加域名 -->
	<insert id="insert" parameterClass="host_setting">
		insert into host_setting
		(userid,enable_host,disable_host,setting_type)
		values
		(#userid#, #enable_host#, #disable_host#, #setting_type#)
	</insert>
	
    <!-- 根据用户ID查询域名 -->
	<select id="selectByUserid" parameterClass="java.lang.String" resultClass="host_setting">
		select * from host_setting where userid = #userid#
	</select>
	
	 <!-- 根据域用户ID修改域名白名单 -->
	<update id="updateEnable_host" parameterClass="host_setting">
	     update host_setting set enable_host=#enable_host# where userid=#userid#
	</update>
	
	<!-- 根据域用户ID修改域名黑名单 -->
	<update id="updateDisable_host" parameterClass="host_setting">
	    update host_setting set disable_host=#disable_host# where userid=#userid#
	</update>
	
	<!-- 根据域用户ID修改域名选择类型 -->
	<update id="updateSetting_type" parameterClass="host_setting">
	   update host_setting set setting_type=#setting_type# where userid=#userid#
	</update>
	
	
  <!-- 根据域用户ID修改域名选择类型 -->
	<update id="updateByUserid" parameterClass="host_setting">
	   update host_setting set enable_host=#enable_host#,disable_host=#disable_host#,setting_type=#setting_type# where userid=#userid#
	</update>
	
</sqlMap>
