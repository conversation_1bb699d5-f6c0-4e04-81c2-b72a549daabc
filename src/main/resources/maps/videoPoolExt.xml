<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="videoPoolExt">
    <typeAlias alias="videoPoolExt" type="com.cc.ovp.domain.VideoPoolExt" />
    <typeAlias alias="doc" type="ayou.util.DOC" />
    
    <sql id="_columns">
        video_pool_id, content
    </sql>

    <insert id="insert" parameterClass="videoPoolExt">
        insert into video_pool_ext (video_pool_id, content)
        values (#videoPoolId#, #content#)
    </insert>
    
    <delete id="delete" parameterClass="long">
        delete from video_pool_ext where video_pool_id = #videoPoolId#
    </delete>
    
    <select id="getById" parameterClass="long" resultClass="videoPoolExt">
        select <include refid="_columns"/> from video_pool_ext where video_pool_id = #videoPoolId#
    </select>
    

</sqlMap>
