<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tbTemporarytraffic">

	<typeAlias alias="tbTemporarytraffic" type="com.cc.AgentPlatform.domain.TbTemporarytraffic" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	  <resultMap id="tbTemporarytrafficResult" class="com.cc.AgentPlatform.domain.TbTemporarytraffic">   
					<result property="id" column="id" nullValue="0"/> 
					<result property="userid" column="userid"/>   
					<result property="del" column="del" nullValue="0"/>  
					<result property="addtime" column="addtime" nullValue="2012/01/01"/>  
					<result property="flow" column="flow" nullValue="0"/> 
					<result property="temptime" column="temptime" nullValue="2012/01/01"/>  
					<result property="type" column="type" nullValue="0"/>   
					<result property="ismain" column="ismain" nullValue="0"/>   
		  
		  </resultMap>
	
	<sql id="_columns">
							id,
							userid,
							del,
							addtime,
							flow,
							temptime,
							type,
							ismain
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ tb_temporarytraffic.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ tb_temporarytraffic.userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ tb_temporarytraffic.del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="addtime"> 
									<isNotEmpty prepend="AND" property="addtime"> 
										<![CDATA[tb_temporarytraffic.addtime<DATE_FORMAT( '$addtime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="flow"> 
									<isNotEmpty prepend="AND" property="flow"> 
										<![CDATA[ tb_temporarytraffic.flow = #flow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="temptime"> 
									<isNotEmpty prepend="AND" property="temptime"> 
										<![CDATA[tb_temporarytraffic.temptime>#temptime#]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="ismain"> 
									<isNotEmpty prepend="AND" property="ismain"> 
										<![CDATA[ tb_temporarytraffic.ismain = #ismain# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="type"> 
									<isNotEmpty prepend="AND" property="type"> 
										<![CDATA[ tb_temporarytraffic.type = #type# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="tbTemporarytraffic">
		insert into tb_temporarytraffic 
		(
							id,
							userid,
							del,
							addtime,
							flow,
							temptime,
							type,
							ismain
		)
		values
		(
							#id#,
							#userid#,
							#del#,
							#addtime#,
							#flow#,
							#temptime#,
							#type#,
							#ismain#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from tb_temporarytraffic where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultMap="tbTemporarytrafficResult">
		select <include refid="_columns"/> from tb_temporarytraffic where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="getSumFlow" parameterClass="doc" resultClass="long">
		select sum(flow) from tb_temporarytraffic where 1=1 <include refid="_condition"/>
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultMap="tbTemporarytrafficResult">
		select <include refid="_columns"/> from tb_temporarytraffic where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
		select <include refid="_columns"/> from tb_temporarytraffic where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultMap="tbTemporarytrafficResult">
		select <include refid="_columns"/> from tb_temporarytraffic where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from tb_temporarytraffic where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultMap="tbTemporarytrafficResult">
		select <include refid="_columns"/> from tb_temporarytraffic
	</select>
	
		<update id="update" parameterClass="tbTemporarytraffic">
		update tb_temporarytraffic set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="addtime"> 
							<isNotEmpty property="addtime"> 
								addtime = #addtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="flow"> 
							<isNotEmpty property="flow"> 
								flow = #flow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="temptime"> 
							<isNotEmpty property="temptime"> 
								temptime = #temptime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="type"> 
							<isNotEmpty property="type"> 
								type = #type# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="ismain"> 
							<isNotEmpty property="ismain"> 
								ismain = #ismain#, 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

