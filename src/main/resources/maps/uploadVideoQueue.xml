<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="uploadVideoQueue">

	<typeAlias alias="uploadVideoQueue" type="com.cc.ovp.domain.UploadVideoQueue" />

    <sql id="_columns">
    	videoPoolId, userid, title, describ, cataid, tag, luping, fileid, originalFilename, 
	    filesize, filePath, clientIp, serverIp, source, status, createdTime, lastModified, 
	    uploadType, ossFilePath, bucketName, lastFilePath, extra, storage_type
    </sql>
    
    <sql id="_condition">
    	where 1=1 
	    <isPropertyAvailable property="videoPoolId"> 
	        <isNotEmpty prepend="AND" property="videoPoolId"> 
	            <![CDATA[ videoPoolId = #videoPoolId# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    <isPropertyAvailable property="userid"> 
	        <isNotEmpty prepend="AND" property="userid"> 
	            <![CDATA[ userid = #userid# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    <isPropertyAvailable property="title"> 
	        <isNotEmpty prepend="AND" property="title"> 
	            <![CDATA[ title = #title# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    <isPropertyAvailable property="describ"> 
	        <isNotEmpty prepend="AND" property="describ"> 
	            <![CDATA[ describ = #describ# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	   <isPropertyAvailable property="cataid"> 
	        <isNotEmpty prepend="AND" property="cataid"> 
	            <![CDATA[ cataid = #cataid# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable> 
	    <isPropertyAvailable property="tag"> 
	        <isNotEmpty prepend="AND" property="tag"> 
	            <![CDATA[ tag = #tag# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    <isPropertyAvailable property="luping"> 
	        <isNotEmpty prepend="AND" property="luping"> 
	            <![CDATA[ luping = #luping# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    <isPropertyAvailable property="fileid"> 
	        <isNotEmpty prepend="AND" property="fileid"> 
	            <![CDATA[ fileid = #fileid# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    <isPropertyAvailable property="originalFilename"> 
	        <isNotEmpty prepend="AND" property="originalFilename"> 
	            <![CDATA[ originalFilename = #originalFilename# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    <isPropertyAvailable property="filesize"> 
	        <isNotEmpty prepend="AND" property="filesize"> 
	            <![CDATA[ filesize = #filesize# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    
	    <isPropertyAvailable property="filePath"> 
	        <isNotEmpty prepend="AND" property="filePath"> 
	            <![CDATA[ filePath = #filePath# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	
	
	    <isPropertyAvailable property="clientIp"> 
	        <isNotEmpty prepend="AND" property="clientIp"> 
	            <![CDATA[ clientIp = #clientIp# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    
	    <isPropertyAvailable property="serverIp"> 
	        <isNotEmpty prepend="AND" property="serverIp"> 
	            <![CDATA[ serverIp like #serverIp#]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	
	
	    <isPropertyAvailable property="source"> 
	        <isNotEmpty prepend="AND" property="source"> 
	            <![CDATA[ source = #source# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	    
	    <isPropertyAvailable property="status"> 
	        <isNotEmpty prepend="AND" property="status"> 
	            <![CDATA[ status = #status# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	
	    <isPropertyAvailable property="createdTime">
	        <isNotEmpty prepend="AND" property="createdTime"> 
	            <![CDATA[ createdTime = #createdTime# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>
	
	    <isPropertyAvailable property="lastModified">
	        <isNotEmpty prepend="AND" property="lastModified"> 
	            <![CDATA[ lastModified = #lastModified# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>

	    <isPropertyAvailable property="uploadType">
	        <isNotEmpty prepend="AND" property="uploadType"> 
	            <![CDATA[ uploadType = #uploadType# ]]>
	        </isNotEmpty>
	    </isPropertyAvailable>

		<isPropertyAvailable property="ossFilePath">
			<isNotEmpty prepend="AND" property="ossFilePath">
				<![CDATA[ ossFilePath = #ossFilePath# ]]>
			</isNotEmpty>
		</isPropertyAvailable>
		<isPropertyAvailable property="bucketName">
			<isNotEmpty prepend="AND" property="bucketName">
				<![CDATA[ bucketName = #bucketName# ]]>
			</isNotEmpty>
		</isPropertyAvailable>
		<isPropertyAvailable property="lastFilePath">
			<isNotEmpty prepend="AND" property="lastFilePath">
				<![CDATA[ lastFilePath = #lastFilePath# ]]>
			</isNotEmpty>
		</isPropertyAvailable>
		<isPropertyAvailable property="extra">
			<isNotEmpty prepend="AND" property="extra">
				<![CDATA[ extra = #extra# ]]>
			</isNotEmpty>
		</isPropertyAvailable>
	</sql>

	<select id="getByVideoPoolId" parameterClass="String" resultClass="com.cc.ovp.domain.UploadVideoQueue" remapResults="true">
        select 
        	<include refid="_columns"/> 
        from upload_video_queue
        where videoPoolId=#videoPoolId#
    </select>

	<select id="queryByVideoPoolId" parameterClass="java.util.Map" resultClass="uploadVideoQueue">
		select <include refid="_columns"/> from upload_video_queue
		where videoPoolId = #videoPoolId#
	</select>

	<select id="listInVideoPoolIds" parameterClass="java.util.Map" resultClass="uploadVideoQueue">
		select <include refid="_columns"/> from upload_video_queue
		where videoPoolId in
		<iterate  prepend="" property="videoPoolIds"  open="("  close=")"  conjunction="," >
			#videoPoolIds[]#
		</iterate>
	</select>

	<select id="listWithStatus" parameterClass="java.util.Map" resultClass="uploadVideoQueue">
		select <include refid="_columns"/> from upload_video_queue
		where status=#status#
		and storage_type IN
		<iterate  prepend="" property="storageTypes"  open="("  close=")"  conjunction="," >
			#storageTypes[]#
		</iterate>

		<dynamic prepend="ORDER BY">
			<isNotEmpty property="orderstr">
				$orderstr$ $sortDirection$
			</isNotEmpty>
		</dynamic>

		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>

	<update id="updateStatus" parameterClass="com.cc.ovp.domain.UploadVideoQueue">
		update upload_video_queue set status=#status#, lastModified=now()
		where videoPoolId=#videoPoolId#;
	</update>

	<update id="updateStatusByOldStatus" parameterClass="java.util.Map">
		update upload_video_queue set status=#newStatus#, lastModified=now()
		where videoPoolId=#videoPoolId# and status=#oldStatus#;
	</update>

	<update id="updateStatusInVideoPoolIdsAndOldStatus" parameterClass="java.util.Map">
		update upload_video_queue set status=#newStatus#, lastModified=now()
		where videoPoolId in
		<iterate  prepend="" property="videoPoolIds"  open="("  close=")"  conjunction="," >
		#videoPoolIds[]#
		</iterate>
		and status=#oldStatus#;
	</update>

	<update id="updateStatusInVideoPoolIds" parameterClass="java.util.Map">
		update upload_video_queue set status=#status#, lastModified=now()
		where videoPoolId in
		<iterate  prepend="" property="videoPoolIds"  open="("  close=")"  conjunction="," >
			#videoPoolIds[]#
		</iterate>;
	</update>

    <update id="updateStatusByCondition" parameterClass="Map">
        update upload_video_queue set status=#updatedStatus# 
	    <include refid="_condition"/>
	    and userid='cca90d24f7'
    </update>

	<update id="insertOrUpdate" parameterClass="com.cc.ovp.domain.UploadVideoQueue">
		insert into upload_video_queue
		(videoPoolId, userid, title, describ, cataid, tag, luping, fileid, originalFilename,
		filesize, filePath, clientIp, serverIp, source, status, createdTime, lastModified,
		uploadType, ossFilePath, bucketName, lastFilePath, extra, storage_type) values
	    (#videoPoolId#, #userid#, #title#, #describ#, #cataid#, #tag#, #luping#, #fileid#, #originalFilename#,
		 #filesize#, #filePath#, #clientIp#, #serverIp#, #source#, #status#, #createdTime#, #lastModified#,
		 #uploadType#, #ossFilePath#, #bucketName#, #lastFilePath#, #extra#, #storage_type#)
		 on duplicate key update
		 status=#status#,
		 filePath=#filePath#,
		 title=#title#,
		 bucketName=#bucketName#,
		 ossFilePath=#ossFilePath#,
		 tag=#tag#,
		 clientIp=#clientIp#,
		 describ=#describ#,
		 filesize=#filesize#,
		 extra=#extra#,
		 source=#source#,
		 cataid=#cataid#,
		 luping=#luping#
	</update>
</sqlMap>

