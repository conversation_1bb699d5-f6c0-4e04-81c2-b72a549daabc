<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="dfs_name">
	<typeAlias alias="name" type="com.cc.ovp.domain.DfsName" />
	<insert id="insert" parameterClass="name">
		insert into dfs_name_$hostId$ (id,src,dst,groupName,fileName,video_pool_id,ptime,indate,del,filetype,type) 
		values (#id#,#src#,#dst#,#groupName#,#fileName#,#video_pool_id#,#ptime#,#indate#,#del#,#filetype#,#type#)
	</insert>

	<select id="select" parameterClass="java.util.Map"  resultClass="name">
		select  *  from dfs_name_$hostId$ where src =  #src#
	</select>
	
	<select id="selectByFileName" parameterClass="java.util.Map"  resultClass="name">
		select  *  from dfs_name_$hostId$ where fileName =  #fileName#
	</select>
	
	<select id="selectList"  resultClass="name">
		select  *  from dfs_name 
	</select>
	<update id="update" parameterClass="name" >
		update dfs_name_$hostId$ set dst = #dst#,groupName= #groupName#,fileName=#fileName#,
		video_pool_id = #video_pool_id#,ptime=#ptime#,indate=#indate#,del=#del#,
		filetype=#filetype#,type=#type#
		where src = #src#
	</update>
	
	<update id="updateDFS" parameterClass="name" >
		update dfs_name_$hostId$ set fileName=#fileName#,
		video_pool_id = #video_pool_id#,ptime=#ptime#,indate=#indate#,filetype=#filetype#,
		type=#type#
		where id = #id#
	</update>
</sqlMap>
