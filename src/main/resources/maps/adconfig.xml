<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="adconfig">

	<typeAlias alias="adMatter" type="com.cc.ovp.domain.AdMatter" />
	<typeAlias alias="advStat" type="com.cc.ovp.domain.AdvStat" />

	<sql id="_columns">
		adid, title, note,userid,matterurl,location,addrurl, isstatis, 
		cpmplay, desccpmplay,playnum,uptime, offtime,startdate,enddate, province,status,
		country,ptime,size,width,height,doublenum,errornum,cataids,poplocation,popuptime,skiptime,skipbutton,skipenabled,displaytext
	</sql>
	

	<insert id="insert" parameterClass="adMatter">
		insert into ad_matter(adid, title, note, userid, location, matterurl, size, addrurl, isstatis, cpmplay, 
		desccpmplay,playnum, uptime, offtime, startdate, enddate, province, country, ptime,status,width,height,doublenum,errornum,cataids,skiptime,skipbutton,skipenabled)
		values (#adid#, #title#, #note#,#userid#,#location#,#matterurl#,#size#,#addrurl#, #isstatis#, 
		#cpmplay#, #desccpmplay#,#playnum#,#uptime#, #offtime#,#startdate#,#enddate#, #province#,#country#,
		#ptime#,#status#,#width#,#height#,#doublenum#,#errornum#,#cataids#,#skiptime#,#skipbutton#,#skipenabled#)
		  
	</insert>

	<update id="update" parameterClass="adMatter">
	update ad_matter set title=#title#,note=#note#,location=#location#,matterurl=#matterurl#,size=#size#,
	addrurl=#addrurl#,isstatis=#isstatis#,cpmplay=#cpmplay#,desccpmplay=#desccpmplay#,playnum=#playnum#,
	uptime=#uptime#,offtime=#offtime#,startdate=#startdate#,enddate=#enddate#,province=#province#,
	status=#status#,doublenum=#doublenum#,errornum=#errornum#,cataids= #cataids#,skiptime=#skiptime#,skipbutton=#skipbutton#,skipenabled=#skipenabled#
	where  userid=#userid# and adid=#adid# 
	
	</update>
	
	<update id="delete" parameterClass="java.util.Map">
		delete from ad_matter where 
		userid=#userid# and adid=#adid#
	</update>
	
	
	<update id="updateTimes" parameterClass="java.util.Map">
		update ad_matter set playnum=#playnum# where adid=#adid#
	</update>
	
	<select id="selectById" parameterClass="java.util.Map" resultClass="adMatter">
		select <include refid="_columns"/> from ad_matter where  userid = #userid# and adid = #adid#
	</select>
	<select id="getAdsByAdid" parameterClass="String" resultClass="adMatter">
		select <include refid="_columns"/>  from ad_matter where  adid = #adid#
	</select>
	<select id="selectList" parameterClass="java.util.Map" resultClass="adMatter">
		select * from ad_matter where  userid=#userid# and location=#location#
		and status != -1 order by ptime desc
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount" parameterClass="java.util.Map" resultClass="int">
		select count(*) from ad_matter where  userid=#userid# and location=#location# and status != -1
	</select>
		<!--  最多可以50个广告在线-->
	<select id="selectOnLineAds" parameterClass="java.util.Map" resultClass="adMatter">
		select <include refid="_columns"/>  from ad_matter where  1=1
		
			<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										userid=#userid#
									</isNotEmpty>
		</isPropertyAvailable>
		
		<isPropertyAvailable property="status"> 
									<isNotEmpty prepend="AND" property="status"> 
										status=#status#
									</isNotEmpty>
		</isPropertyAvailable>
		
		<isPropertyAvailable property="cataid">
									<isNotEmpty prepend="AND" property="cataid"> 
										cataids like CONCAT('%', #cataid#, '%')
									</isNotEmpty>
		</isPropertyAvailable>
		
		<isPropertyAvailable property="location"> 
									<isNotEmpty prepend="AND" property="location"> 
										location=#location# 
									</isNotEmpty>
		</isPropertyAvailable>
	</select>
	
	<select id="getAllAdid"  resultClass="adMatter">
		select * from ad_matter where   status != -1 and  enddate>now()
	</select>
	
	<select id="getAllAdByUserid"  parameterClass="java.util.Map" resultClass="adMatter">
		select * from ad_matter where   status != -1 and userid=#userid# order by ptime desc
	</select>
	
		<select id="getAllAdByMap" parameterClass="java.util.Map" resultClass="adMatter" >
	   select * from ad_matter where userid = #userid# and status != -1
	   
	   	<isPropertyAvailable property="startDate"> 
									<isNotEmpty prepend="AND" property="startDate"> 
										<![CDATA[  DATEDIFF(#startDate# , startdate)> 0  ]]>
									</isNotEmpty>
		</isPropertyAvailable>
								
	    	<isPropertyAvailable property="endDate"> 
									<isNotEmpty prepend="AND" property="endDate"> 
										<![CDATA[   DATEDIFF(#endDate# , enddate) < 0  ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
	    
		order by ptime desc  
	   <dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	  
	</select>
	
	<select id="getAllCountByMap"  parameterClass="java.util.Map" resultClass="int" >
		  select count(*) from ad_matter where userid = #userid# and status != -1
		     	<isPropertyAvailable property="startDate"> 
									<isNotEmpty prepend="AND" property="startDate"> 
										<![CDATA[  DATEDIFF(#startDate# , startdate)> 0  ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
	    	<isPropertyAvailable property="endDate"> 
									<isNotEmpty prepend="AND" property="endDate"> 
										<![CDATA[   DATEDIFF(#endDate# , enddate) < 0  ]]>
									</isNotEmpty>
								</isPropertyAvailable>
	</select>

</sqlMap>
