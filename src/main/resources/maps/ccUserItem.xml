<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="ccUserItem">

	<typeAlias alias="ccUserItem" type="com.cc.usercenter.domain.CcUserItem" />
	<typeAlias alias="doc" type="ayou.util.DOC" />
	
	  <resultMap id="CcUserItemResult" class="com.cc.usercenter.domain.CcUserItem">   
		  <result property="id" column="id"  nullValue="0"/>   
		  <result property="item" column="item" nullValue=""/> 
		  <result property="del" column="del" nullValue="0"/>   
		  <result property="itemurl" column="itemurl" nullValue=""/>   
		  <result property="txt" column="txt"/>   
		  <result property="endtime" column="endtime" nullValue="2012/04/13 00:00:00"/>
		  <result property="addm" column="addm"   nullValue="0"/>
		  <result property="munber" column="munber"/>
		  <result property="type" column="type"/>
		  <result property="agentshow" column="agentshow" nullValue="1"/>
		  <result property="price" column="price" nullValue="0"/>
		  <result property="isnum" column="isnum" nullValue="0"/> 
     </resultMap>
	  

	<sql id="_columns">
							cc_user_item.id,
							cc_user_item.item,
							cc_user_item.del,
							cc_user_item.itemurl,
							cc_user_item.txt,
							cc_user_item.endtime,
							cc_user_item.addm,
							cc_user_item.munber,
							cc_user_item.type,
							cc_user_item.agentshow,
							cc_user_item.price,
							cc_user_item.isnum
	</sql>
	
	<statement id="getCcUserItemByUserId" parameterClass="String"  resultMap="CcUserItemResult">
    <![CDATA[ 
       SELECT cc_user_item.* from ccuserjoinitem left join cc_user_item on cc_user_item.id=ccuserjoinitem.cc_user_item_id WHERE ccuserjoinitem.userid=#userid# 
    ]]>
    </statement>
    
	<statement id="getCcUserItemByPackageTypeid" parameterClass="long"  resultMap="CcUserItemResult">
    <![CDATA[
       SELECT cc_user_item.* from packagetype_item left join cc_user_item on cc_user_item.id=packagetype_item.cc_user_item_id WHERE cc_user_item.del=0 and packagetype_item.del=0 and packagetype_item.packageType_id=#packageTypeid# 
    ]]>
    </statement>
	
	<sql id="_condition">
								<isPropertyAvailable property="addm"> 
									<isNotEmpty prepend="AND" property="addm"> 
										<![CDATA[ cc_user_item.addm = #addm# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ cc_user_item.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="agentshow"> 
									<isNotEmpty prepend="AND" property="agentshow"> 
										<![CDATA[ cc_user_item.agentshow = #agentshow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="isnum"> 
									<isNotEmpty prepend="AND" property="isnum"> 
										<![CDATA[ cc_user_item.isnum = #isnum# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="item"> 
									<isNotEmpty prepend="AND" property="item"> 
										<![CDATA[ cc_user_item.item = #item# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ cc_user_item.del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="price"> 
									<isNotEmpty prepend="AND" property="price"> 
										<![CDATA[ cc_user_item.price = #price# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="itemurl"> 
									<isNotEmpty prepend="AND" property="itemurl"> 
										<![CDATA[ cc_user_item.itemurl = #itemurl# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="txt"> 
									<isNotEmpty prepend="AND" property="txt"> 
										<![CDATA[ cc_user_item.txt = #txt# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="endtime"> 
									<isNotEmpty prepend="AND" property="endtime"> 
										<![CDATA[ cc_user_item.endtime = #endtime# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								

						
	</sql>
	
	<insert id="insert" parameterClass="ccUserItem">
		insert into cc_user_item 
		(
							item,
							del,
							itemurl,
							txt,
							endtime,
							addm,
							munber,
							type,
							agentshow,
							price,
							isnum
		)
		values
		(
							#item#,
							#del#,
							#itemurl#,
							#txt#,
							#endtime#,
							#addm#,
							#munber#,
							#type#,
							#agentshow#,
							#price#,
							#isnum#
		)
		<selectKey resultClass="long" keyProperty="id" type="post">   
             <![CDATA[SELECT LAST_INSERT_ID() AS ID ]]>  
         </selectKey>  
	</insert>

	<delete id="delete" parameterClass="long">
		delete from cc_user_item where 1=1 and
							id = #id#
	</delete>
	
	<delete id="del" parameterClass="String">
		delete from cc_user_item where 1=1 and
							userid = #userid#
	</delete>
	
	<select id="getById" parameterClass="long"  resultMap="CcUserItemResult">
		select <include refid="_columns"/> from cc_user_item where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc"  resultMap="CcUserItemResult">
		select <include refid="_columns"/> from cc_user_item where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
		select <include refid="_columns"/> from cc_user_item where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map"  resultMap="CcUserItemResult">
		select <include refid="_columns"/> from cc_user_item where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from cc_user_item where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"   resultMap="CcUserItemResult">
		select <include refid="_columns"/> from cc_user_item
	</select>
	
		<update id="update" parameterClass="ccUserItem">
		update cc_user_item set
						<isPropertyAvailable property="price"> 
							<isNotEmpty property="price"> 
								price = #price# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="agentshow"> 
							<isNotEmpty property="agentshow"> 
								agentshow = #agentshow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="item"> 
							<isNotEmpty property="item"> 
								item = #item# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="itemurl"> 
							<isNotEmpty property="itemurl"> 
								itemurl = #itemurl# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="endtime"> 
							<isNotEmpty property="endtime"> 
								endtime = #endtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="txt"> 
							<isNotEmpty property="txt"> 
								txt = #txt# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="addm"> 
							<isNotEmpty property="addm"> 
								addm = #addm# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="munber"> 
							<isNotEmpty property="munber"> 
								munber = #munber# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	    <isPropertyAvailable property="type"> 
							<isNotEmpty property="type"> 
								type = #type# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
				   	     <isPropertyAvailable property="isnum"> 
							<isNotEmpty property="isnum"> 
								isnum = #isnum# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

