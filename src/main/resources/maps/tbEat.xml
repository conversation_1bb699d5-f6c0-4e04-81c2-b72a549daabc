<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tbEat">

	<typeAlias alias="tbEat" type="com.cc.ovp.domain.TbEat" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							tb_eat.id,
							tb_eat.nameid,
							tb_eat.price,
							tb_eat.eat,
							tb_eat.addtime,
							tb_eat.del
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ tb_eat.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="nameid"> 
									<isNotEmpty prepend="AND" property="nameid"> 
										<![CDATA[ tb_eat.nameid = #nameid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="price"> 
									<isNotEmpty prepend="AND" property="price"> 
										<![CDATA[ tb_eat.price = #price# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="eat"> 
									<isNotEmpty prepend="AND" property="eat"> 
										<![CDATA[ tb_eat.eat = #eat# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="addtime"> 
									<isNotEmpty prepend="AND" property="addtime"> 
										<![CDATA[tb_eat.addtime<DATE_FORMAT( '$addtime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ tb_eat.del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="tbEat">
		insert into tb_eat 
		(
							id,
							nameid,
							price,
							eat,
							addtime,
							del
		)
		values
		(
							#id#,
							#nameid#,
							#price#,
							#eat#,
							#addtime#,
							#del#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from tb_eat where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="tbEat">
		select <include refid="_columns"/> from tb_eat where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="tbEat">
		select <include refid="_columns"/>,tb_name.name from tb_eat left join tb_name on tb_eat.nameid=tb_name.id where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
		select <include refid="_columns"/> from tb_eat where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="tbEat">
		select <include refid="_columns"/> from tb_eat where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from tb_eat where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="tbEat">
		select <include refid="_columns"/> from tb_eat
	</select>
	
		<update id="update" parameterClass="tbEat">
		update tb_eat set
		
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="nameid"> 
							<isNotEmpty property="nameid"> 
								nameid = #nameid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="price"> 
							<isNotEmpty property="price"> 
								price = #price# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="eat"> 
							<isNotEmpty property="eat"> 
								eat = #eat# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="addtime"> 
							<isNotEmpty property="addtime"> 
								addtime = #addtime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

