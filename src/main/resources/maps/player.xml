<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="player">

	<typeAlias alias="player" type="com.cc.ovp.domain.Player" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">autoid,userid,title,pic,path,status,marginh,marginw,color_bar,color_mc,color_bg</sql>
	
	<insert id="insert" parameterClass="player">
		insert into player 
		(autoid,userid,title,pic,path,status,marginw,marginh,color_bar,color_mc,color_bg)
		values
		(#autoid#, #userid#, #title#, #pic#, #path#, #status#, #marginw#, #marginh#, #color_bar#, #color_mc#, #color_bg#)
	</insert>

	<update id="update" parameterClass="player">
		update player set
		
		<isPropertyAvailable property="userid"> 
			<isNotEmpty property="userid"> 
				userid = #userid# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="title"> 
			<isNotEmpty property="title"> 
				title = #title# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="pic"> 
			<isNotEmpty property="pic"> 
				pic = #pic# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="path"> 
			<isNotEmpty property="path"> 
				path = #path# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="status"> 
			<isNotEmpty property="status"> 
				status = #status# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="marginw"> 
			<isNotEmpty property="marginw"> 
				marginw = #marginw# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	    <isPropertyAvailable property="marginh"> 
			<isNotEmpty property="marginh"> 
				marginh = #marginh# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
   	      <isPropertyAvailable property="color_bar"> 
			<isNotEmpty property="color_bar"> 
				color_bar = #color_bar# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	      <isPropertyAvailable property="color_mc"> 
			<isNotEmpty property="color_mc"> 
				color_mc = #color_mc# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	      <isPropertyAvailable property="color_bg"> 
			<isNotEmpty property="color_bg"> 
				color_bg = #color_bg# , 
			</isNotEmpty>
   	    </isPropertyAvailable>
   	    
		autoid = #autoid# 
		where autoid = #autoid#
	</update>
	
	
	
	
	<delete id="delete" parameterClass="long">
		delete from player where autoid = #autoid#
	</delete>
	
	<select id="selectById" parameterClass="long" resultClass="player">
		select <include refid="_columns"/> from player where autoid = #autoid#
	</select>
	
	<select id="selectByUserId" parameterClass="String" resultClass="player">
		select <include refid="_columns"/> from player where userid = #userid#
	</select>
	
	<select id="getCount" parameterClass="doc" resultClass="int">
		select count(*) from player where 1=1
			<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			
			<isPropertyAvailable property="status"> 
				<isNotEmpty prepend="AND" property="status"> 
					<![CDATA[status = #status# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			
			<isPropertyAvailable property="userid9"> 
				<isNotEmpty prepend="or" property="userid9"> 
					userid = "9"
				</isNotEmpty>
			</isPropertyAvailable>
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="player">
		select <include refid="_columns"/> from player where 1=1
			<isPropertyAvailable property="status"> 
				<isNotEmpty prepend="AND" property="status"> 
					<![CDATA[status = #status# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="userid"> 
				<isNotEmpty prepend="AND" property="userid"> 
					<![CDATA[userid = #userid# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="path"> 
				<isNotEmpty prepend="AND" property="path"> 
					<![CDATA[path = #path# ]]>
				</isNotEmpty>
			</isPropertyAvailable>
			<isPropertyAvailable property="userid9"> 
				<isNotEmpty prepend="or" property="userid9"> 
					userid = "9"
				</isNotEmpty>
			</isPropertyAvailable>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListCount" resultClass="int">
		select count(*) from player
	</select>

	

</sqlMap>
