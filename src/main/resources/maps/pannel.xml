<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="pannel">
	<typeAlias alias="pannel" type="com.cc.ovp.domain.Pannel" />
	<sql id="_columns">
		userid,ups,total,spaceSize,remainSize,days,times,allflowsize,cost,totalCost,fullDay,reFlowSize,flowXML,nameXML,spaceXML,ptime,indate
	</sql>
	<insert id="insert"  parameterClass="pannel">
	insert into pannel (userid,ups,total,spaceSize,remainSize,days,times,allflowsize,cost,totalCost,fullDay,reFlowSize,flowXML,nameXML,spaceXML,ptime,indate)
		values
		(#userid#,#ups#,#total#,#spaceSize#,#remainSize#,#days#,#times#,#allflowsize#,#cost#,#totalCost#,#fullDay#,#reFlowSize#,#flowXML#,#nameXML#,#spaceXML#,#ptime#,#indate#)
	</insert>
	
	<select id="select" parameterClass="java.util.Map" resultClass="pannel">
		select <include refid="_columns"/> from pannel
		where userid=#userid# and indate=#indate#
	</select>
	
	<update id="update" parameterClass="pannel">
		update pannel 
		set ups=#ups#,total=#total#,spaceSize=#spaceSize#,remainSize=#remainSize#,days=#days#,
		times=#times#,allflowsize=#allflowsize#,cost=#cost#,totalCost=#totalCost#,fullDay=#fullDay#,
		reFlowSize=#reFlowSize#,flowXML=#flowXML#,nameXML=#nameXML#,spaceXML=#spaceXML#,ptime=#ptime#
		where userid=#userid# and indate=#indate#
	</update>
	<delete id="delete" parameterClass="String">
		delete from pannel where userid = #userid# and indate=#indate#
	</delete>
	<select id="selectListCount" resultClass="int">
		select count(*) from pannel
	</select>
	<select id="selectList" parameterClass="java.util.Map" resultClass="pannel">
		select * from pannel
		<dynamic prepend="order by">
			<isNotEmpty property="sortname">$sortname$</isNotEmpty>
			<isNotEmpty property="sortorder">  $sortorder$</isNotEmpty>
		</dynamic>
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>	
</sqlMap>