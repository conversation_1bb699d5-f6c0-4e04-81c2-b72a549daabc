<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="orderdetailed">

	<typeAlias alias="orderdetailed" type="com.cc.AgentPlatform.domain.Orderdetailed" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							id,
							userid,
							cmoney,
							retime,
							currency,
							payment,
							del,
							datetime,
							packageid,
							flow,
							validity,
							mprice,
							status,
							space
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

								<isPropertyAvailable property="space"> 
									<isNotEmpty prepend="AND" property="space"> 
										<![CDATA[ space = #space# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="cmoney"> 
									<isNotEmpty prepend="AND" property="cmoney"> 
										<![CDATA[ cmoney = #cmoney# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="retime"> 
									<isNotEmpty prepend="AND" property="retime"> 
										<![CDATA[retime<DATE_FORMAT( '$retime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="currency"> 
									<isNotEmpty prepend="AND" property="currency"> 
										<![CDATA[ currency = #currency# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="payment"> 
									<isNotEmpty prepend="AND" property="payment"> 
										<![CDATA[ payment = #payment# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="datetime"> 
									<isNotEmpty prepend="AND" property="datetime"> 
										<![CDATA[datetime<DATE_FORMAT( '$datetime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="packageid"> 
									<isNotEmpty prepend="AND" property="packageid"> 
										<![CDATA[ packageid = #packageid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="flow"> 
									<isNotEmpty prepend="AND" property="flow"> 
										<![CDATA[ flow = #flow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="validity"> 
									<isNotEmpty prepend="AND" property="validity"> 
										<![CDATA[validity<DATE_FORMAT( '$validity$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="mprice"> 
									<isNotEmpty prepend="AND" property="mprice"> 
										<![CDATA[ mprice = #mprice# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="status"> 
									<isNotEmpty prepend="AND" property="status"> 
										<![CDATA[ status = #status# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
	</sql>
	
	<insert id="insert" parameterClass="orderdetailed">
		insert into orderdetailed 
		(
							id,
							userid,
							cmoney,
							retime,
							currency,
							payment,
							del,
							datetime,
							packageid,
							flow,
							validity,
							mprice,
							status,
							space
		)
		values
		(
							#id#,
							#userid#,
							#cmoney#,
							#retime#,
							#currency#,
							#payment#,
							#del#,
							#datetime#,
							#packageid#,
							#flow#,
							#validity#,
							#mprice#,
							#status#,
							#space#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from orderdetailed where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="orderdetailed">
		select <include refid="_columns"/> from orderdetailed where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="orderdetailed">
		select <include refid="_columns"/> from orderdetailed where 1=1 
			<include refid="_condition"/>

		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="orderdetailed">
		select <include refid="_columns"/> from orderdetailed where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from orderdetailed where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="orderdetailed">
		select <include refid="_columns"/> from orderdetailed
	</select>
	
		<update id="update" parameterClass="orderdetailed">
		update orderdetailed set
						<isPropertyAvailable property="space"> 
							<isNotEmpty property="space"> 
								space = #space# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="cmoney"> 
							<isNotEmpty property="cmoney"> 
								cmoney = #cmoney# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="retime"> 
							<isNotEmpty property="retime"> 
								retime = #retime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="currency"> 
							<isNotEmpty property="currency"> 
								currency = #currency# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="payment"> 
							<isNotEmpty property="payment"> 
								payment = #payment# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="datetime"> 
							<isNotEmpty property="datetime"> 
								datetime = #datetime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="packageid"> 
							<isNotEmpty property="packageid"> 
								packageid = #packageid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="flow"> 
							<isNotEmpty property="flow"> 
								flow = #flow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="validity"> 
							<isNotEmpty property="validity"> 
								validity = #validity# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="mprice"> 
							<isNotEmpty property="mprice"> 
								mprice = #mprice# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="status"> 
							<isNotEmpty property="status"> 
								status = #status# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

