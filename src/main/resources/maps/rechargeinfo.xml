<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE sqlMap
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="rechargeinfo">

	<typeAlias alias="rechargeinfo" type="com.cc.AgentPlatform.domain.Rechargeinfo" />
	<typeAlias alias="doc" type="ayou.util.DOC" />

	<sql id="_columns">
							id,
							orderid,
							currency,
							status,
							cmoney,
							description,
							userid,
							payment,
							datetime,
							flow,
							available_traffic_id,
							del,
							space
	</sql>
	
	<sql id="_condition">
								<isPropertyAvailable property="id"> 
									<isNotEmpty prepend="AND" property="id"> 
										<![CDATA[ rechargeinfo.id = #id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								<isPropertyAvailable property="space"> 
									<isNotEmpty prepend="AND" property="space"> 
										<![CDATA[ rechargeinfo.space = #space# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="orderid"> 
									<isNotEmpty prepend="AND" property="orderid"> 
										<![CDATA[ rechargeinfo.orderid = #orderid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="currency"> 
									<isNotEmpty prepend="AND" property="currency"> 
										<![CDATA[ rechargeinfo.currency = #currency# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="status"> 
									<isNotEmpty prepend="AND" property="status"> 
										<![CDATA[ rechargeinfo.status = #status# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="cmoney"> 
									<isNotEmpty prepend="AND" property="cmoney"> 
										<![CDATA[ rechargeinfo.cmoney = #cmoney# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="description"> 
									<isNotEmpty prepend="AND" property="description"> 
										<![CDATA[ rechargeinfo.description = #description# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="userid"> 
									<isNotEmpty prepend="AND" property="userid"> 
										<![CDATA[ rechargeinfo.userid = #userid# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="payment"> 
									<isNotEmpty prepend="AND" property="payment"> 
										<![CDATA[ rechargeinfo.payment = #payment# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="datetime"> 
									<isNotEmpty prepend="AND" property="datetime"> 
										<![CDATA[rechargeinfo.datetime<DATE_FORMAT( '$datetime$', '%Y-%m-%d' ) )]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="flow"> 
									<isNotEmpty prepend="AND" property="flow"> 
										<![CDATA[ rechargeinfo.flow = #flow# ]]>
									</isNotEmpty>
								</isPropertyAvailable>

						
								<isPropertyAvailable property="available_traffic_id"> 
									<isNotEmpty prepend="AND" property="available_traffic_id"> 
										<![CDATA[ rechargeinfo.available_traffic_id = #available_traffic_id# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="startdate"> 
									<isNotEmpty prepend="AND" property="startdate"> 
										<![CDATA[ consumption.datetime > #startdate# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="enddate"> 
									<isNotEmpty prepend="AND" property="enddate"> 
										<![CDATA[ consumption.datetime < #enddate# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
						
								<isPropertyAvailable property="del"> 
									<isNotEmpty prepend="AND" property="del"> 
										<![CDATA[ rechargeinfo.del = #del# ]]>
									</isNotEmpty>
								</isPropertyAvailable>
								
								<isPropertyAvailable property="userids"> 
									<isNotEmpty prepend="AND" property="userids"> 
										rechargeinfo.userid in
									    <iterate open="(" close=")" conjunction="," property="userids">
							                #userids[]#
							            </iterate> 
									</isNotEmpty>
								</isPropertyAvailable>
						
	</sql>
	
	<insert id="insert" parameterClass="rechargeinfo">
		insert into rechargeinfo 
		(
							id,
							orderid,
							currency,
							status,
							cmoney,
							description,
							userid,
							payment,
							datetime,
							flow,
							available_traffic_id,
							del,
							space
		)
		values
		(
							#id#,
							#orderid#,
							#currency#,
							#status#,
							#cmoney#,
							#description#,
							#userid#,
							#payment#,
							#datetime#,
							#flow#,
							#available_traffic_id#,
							#del#,
							#space#
		)
	</insert>

	<delete id="delete" parameterClass="long">
		delete from rechargeinfo where 1=1 and
							id = #id#
	</delete>
	
	<select id="getById" parameterClass="long" resultClass="rechargeinfo">
		select <include refid="_columns"/> from rechargeinfo where 1=1 and
							id = #id#
		 
	</select>
	
	<select id="selectList" parameterClass="doc" resultClass="rechargeinfo">
		select <include refid="_columns"/> from rechargeinfo where 1=1 
			<include refid="_condition"/>

		<!--   <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic> -->  
		  
		ORDER BY rechargeinfo.datetime desc
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	
	<select id="selectListDoc" parameterClass="doc" resultClass="doc">
	
	SELECT * from ((SELECT  rechargeinfo.cmoney,rechargeinfo.datetime,rechargeinfo.userid,rechargeinfo.orderid, 1 as type FROM rechargeinfo where 1=1) 
	UNION 
	(SELECT consumption.cmoney,consumption.datetime,consumption.userid,null as orderid, 0 as type FROM consumption where 1=1)) as a where 1=1 
			<isPropertyAvailable property="userid"> 
			<isNotEmpty prepend="AND" property="userid"> 
				<![CDATA[ userid = #userid# ]]>
			</isNotEmpty>
		</isPropertyAvailable>
	 ORDER BY a.datetime desc
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	
	<select id="selectListMap" parameterClass="java.util.Map" resultClass="rechargeinfo">
		select <include refid="_columns"/> from rechargeinfo where 1=1 
			<include refid="_condition"/>
		  <dynamic prepend="ORDER BY">  
		   <isNotEmpty property="orderstr">  
		    $orderstr$ $sortDirection$
		   </isNotEmpty>  
		  </dynamic>  
		
		<dynamic prepend="limit">
			<isNotEmpty property="firstResult">#firstResult#</isNotEmpty>
			<isNotEmpty property="maxResult">, #maxResult#</isNotEmpty>
		</dynamic>
	</select>
	
	<select id="getCount" resultClass="int">
		select count(*) from rechargeinfo where 1=1 
			<include refid="_condition"/>
	</select>

	<select id="all"  resultClass="rechargeinfo">
		select <include refid="_columns"/> from rechargeinfo
	</select>
	
		<update id="update" parameterClass="rechargeinfo">
		update rechargeinfo set
						<isPropertyAvailable property="space"> 
							<isNotEmpty property="space"> 
								space = #space# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="id"> 
							<isNotEmpty property="id"> 
								id = #id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="orderid"> 
							<isNotEmpty property="orderid"> 
								orderid = #orderid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="currency"> 
							<isNotEmpty property="currency"> 
								currency = #currency# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="status"> 
							<isNotEmpty property="status"> 
								status = #status# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="cmoney"> 
							<isNotEmpty property="cmoney"> 
								cmoney = #cmoney# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="description"> 
							<isNotEmpty property="description"> 
								description = #description# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="userid"> 
							<isNotEmpty property="userid"> 
								userid = #userid# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="payment"> 
							<isNotEmpty property="payment"> 
								payment = #payment# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="datetime"> 
							<isNotEmpty property="datetime"> 
								datetime = #datetime# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="flow"> 
							<isNotEmpty property="flow"> 
								flow = #flow# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="available_traffic_id"> 
							<isNotEmpty property="available_traffic_id"> 
								available_traffic_id = #available_traffic_id# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
						<isPropertyAvailable property="del"> 
							<isNotEmpty property="del"> 
								del = #del# , 
							</isNotEmpty>
				   	    </isPropertyAvailable>
							id = #id#
		where 
							id = #id#
	</update>


</sqlMap>

