<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap      
    PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN"      
    "http://ibatis.apache.org/dtd/sql-map-2.dtd">

<sqlMap namespace="groupInfo">
    <typeAlias alias="groupInfo" type="com.cc.ovp.domain.GroupInfo" />

    <select id="getSourceIPByGroupName" parameterClass="String" resultClass="java.lang.String">
        select source_ip
        from group_info 
        where groupName = #groupName#
    </select>
</sqlMap>