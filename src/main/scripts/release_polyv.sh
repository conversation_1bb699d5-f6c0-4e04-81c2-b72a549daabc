#!/bin/sh
#
# 该脚本用于发布POLYV应用。发布的步骤如下：
# 1) 在SVN中打个tag，签注一个版本号，比如1.0.1
# 2) 运行脚本release_polyv.sh --version <version>
# 例如：./release_polyv.sh --version 1.0.1
# 3) 重启Resin
# 
# author: Xingning.Ou
# created: 2012.10.19

usage() {
    echo "该脚本用于发布POLYV应用。发布的步骤如下："
    echo "1) 在SVN中打个tag，签注一个版本号，例如1.0.1"
    echo "2) 运行脚本release_polyv.sh --version <version>"
    echo "3) 重启Resin"
}

ARG_COUNT=$#
if [ $ARG_COUNT -lt 2 ]; then
    usage
    exit 1
fi

VERSION_ARG=$1
VERSION_VAL=$2

# 应用的前置目录
APP_BASE_PATH=/home/<USER>/polyv
mkdir -p ${APP_BASE_PATH}

# 检查是否需要从SVN中下载源码
SVN_PATH=svn://svn.asdtv.com/repos/polyv/tags/${VERSION_VAL}
DEST_PATH=/home/<USER>/polyv/release-history/polyv-${VERSION_VAL}
if [ ! -d "${DEST_PATH}" ]; then
    # 从SVN中下载源码
    mkdir -p ${DEST_PATH}
    svn checkout --username server --password u_server_2012 ${SVN_PATH} ${DEST_PATH}
fi

# 更新并编译代码
cd ${DEST_PATH}
svn update --username server --password u_server_2012
STATUS=`ant build | grep "BUILD SUCCESSFUL"`
if [ "$STATUS" != "" ]; then
    # 删除原符号链接，并重新建立新链接
    SYM_LINK=${APP_BASE_PATH}/release-current
    rm -rf ${SYM_LINK}
    ln -s ${DEST_PATH}/dist ${SYM_LINK}
    
    # 修改脚本的执行属性
    chmod 755 ${SYM_LINK}/webapp/WEB-INF/sh/*.sh
    
    # 修改属主为用户web
    if grep -q "^web:" /etc/passwd; then
        chown -R web:web ${SYM_LINK}
        chown -R web:web ${SYM_LINK}/webapp
    fi
    
    echo "Release Successful"
else
    rm -rf ${DEST_PATH}
    echo "Release Failure"
fi
