#!/bin/sh
#
# 该脚本用于发布POLYV应用trunk上的代码。
# 1) 运行脚本trunk_polyv.sh 
# 2) 重启Resin
# 
# author: Xingning.Ou
# created: 2012.10.19

# 应用的前置目录
APP_BASE_PATH=/home/<USER>/polyv
mkdir -p ${APP_BASE_PATH}

# 检查是否需要从SVN中下载源码
SVN_PATH=svn://svn.asdtv.com/repos/polyv/trunk
DEST_PATH=/home/<USER>/polyv/release-history/polyv-trunk
if [ ! -d "${DEST_PATH}" ]; then
    # 从SVN中下载源码
    mkdir -p ${DEST_PATH}
    svn checkout --username server --password u_server_2012 ${SVN_PATH} ${DEST_PATH}
fi

# 更新并编译代码
cd ${DEST_PATH}
svn update --username server --password u_server_2012
STATUS=`ant build | grep "BUILD SUCCESSFUL"`
if [ "$STATUS" != "" ]; then
    # 删除原符号链接，并重新建立新链接
    SYM_LINK=${APP_BASE_PATH}/release-current
    rm -rf ${SYM_LINK}
    ln -s ${DEST_PATH}/dist ${SYM_LINK}
    
    # 修改脚本的执行属性
    chmod 755 ${SYM_LINK}/webapp/WEB-INF/sh/*.sh
    
    # 修改属主为用户web
    if grep -q "^web:" /etc/passwd; then
        chown -R web:web ${SYM_LINK}
        chown -R web:web ${SYM_LINK}/webapp
    fi
    
    echo "Release Successful"
else
    rm -rf ${DEST_PATH}
    echo "Release Failure"
fi
