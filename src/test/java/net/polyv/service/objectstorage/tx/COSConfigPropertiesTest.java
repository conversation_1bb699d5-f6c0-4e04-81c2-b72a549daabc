package net.polyv.service.objectstorage.tx;

import com.ctrip.framework.apollo.Config;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description COSConfigProperties 测试类
 */
public class COSConfigPropertiesTest {

    @Mock
    private Config mockConfig;

    private COSConfigProperties cosConfigProperties;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 模拟 Apollo 配置
        setupMockConfig();
        
        // 创建配置对象
        cosConfigProperties = new COSConfigProperties(mockConfig);
    }

    private void setupMockConfig() {
        // 模拟全局配置
        when(mockConfig.getProperty("object.storage.tx.appId", "")).thenReturn("test-app-id");
        when(mockConfig.getProperty("object.storage.tx.secretId", "")).thenReturn("test-secret-id");
        when(mockConfig.getProperty("object.storage.tx.secretKey.privateKey", "")).thenReturn("test-secret-key");

        // 模拟客户端配置
        when(mockConfig.getProperty("object.storage.tx.clients.default.name", "")).thenReturn("test-bucket");
        when(mockConfig.getProperty("object.storage.tx.clients.default.region", "")).thenReturn("ap-guangzhou");
        when(mockConfig.getProperty("object.storage.tx.clients.default.domain", "")).thenReturn("test.domain.com");
        when(mockConfig.getProperty("object.storage.tx.clients.default.endpoint", "")).thenReturn("cos.ap-guangzhou.myqcloud.com");
        when(mockConfig.getProperty("object.storage.tx.clients.default.httpProtocol", "https")).thenReturn("https");
        when(mockConfig.getProperty("object.storage.tx.clients.default.userAgent", "")).thenReturn("test-agent");

        // 模拟数值配置
        when(mockConfig.getLongProperty("object.storage.tx.clients.default.signExpired", 3600L)).thenReturn(7200L);
        when(mockConfig.getIntProperty("object.storage.tx.clients.default.connectionRequestTimeout", -1)).thenReturn(5000);
        when(mockConfig.getIntProperty("object.storage.tx.clients.default.connectionTimeout", 30000)).thenReturn(60000);
        when(mockConfig.getIntProperty("object.storage.tx.clients.default.socketTimeout", 30000)).thenReturn(45000);
        when(mockConfig.getIntProperty("object.storage.tx.clients.default.maxConnectionsCount", 1024)).thenReturn(2048);
        when(mockConfig.getIntProperty("object.storage.tx.clients.default.idleConnectionAlive", 60000)).thenReturn(120000);
        when(mockConfig.getIntProperty("object.storage.tx.clients.default.readLimit", 262145)).thenReturn(524290);

        // 模拟对象配置
        when(mockConfig.getIntProperty("object.storage.tx.objects.default.signatureExpireDays", 1)).thenReturn(7);
        when(mockConfig.getIntProperty("object.storage.tx.objects.default.restoreExpireDays", 1)).thenReturn(3);

        // 模拟配置键获取
        Set<String> propertyNames = new HashSet<>();
        propertyNames.add("object.storage.tx.appId");
        propertyNames.add("object.storage.tx.secretId");
        propertyNames.add("object.storage.tx.secretKey.privateKey");
        propertyNames.add("object.storage.tx.clients.default.name");
        propertyNames.add("object.storage.tx.clients.default.region");
        propertyNames.add("object.storage.tx.objects.default.signatureExpireDays");
        when(mockConfig.getPropertyNames()).thenReturn(propertyNames);
    }

    @Test
    public void testInitProperties() {
        // 执行初始化
        cosConfigProperties.initProperties();

        // 验证全局配置
        assertEquals("test-app-id", cosConfigProperties.getAppId());
        assertEquals("test-secret-id", cosConfigProperties.getSecretId());
        assertEquals("test-secret-key", cosConfigProperties.getSecretKey().getPrivateKey());

        // 验证客户端配置
        assertFalse(cosConfigProperties.getClients().isEmpty());
        assertTrue(cosConfigProperties.getClients().containsKey("default"));

        COSConfigProperties.ClientProperties clientProps = cosConfigProperties.getClients().get("default");
        assertNotNull(clientProps);
        assertEquals("test-bucket", clientProps.getName());
        assertEquals("ap-guangzhou", clientProps.getRegion());
        assertEquals("test.domain.com", clientProps.getDomain());
        assertEquals("cos.ap-guangzhou.myqcloud.com", clientProps.getEndpoint());
        assertEquals("test-agent", clientProps.getUserAgent());

        // 验证数值配置
        assertEquals(Long.valueOf(7200L), clientProps.getSignExpired());
        assertEquals(Integer.valueOf(5000), clientProps.getConnectionRequestTimeout());
        assertEquals(Integer.valueOf(60000), clientProps.getConnectionTimeout());
        assertEquals(Integer.valueOf(45000), clientProps.getSocketTimeout());
        assertEquals(Integer.valueOf(2048), clientProps.getMaxConnectionsCount());
        assertEquals(Integer.valueOf(120000), clientProps.getIdleConnectionAlive());
        assertEquals(Integer.valueOf(524290), clientProps.getReadLimit());

        // 验证对象配置
        assertFalse(cosConfigProperties.getObjects().isEmpty());
        assertTrue(cosConfigProperties.getObjects().containsKey("default"));

        COSConfigProperties.ObjectProperties objectProps = cosConfigProperties.getObjects().get("default");
        assertNotNull(objectProps);
        assertEquals(Duration.ofDays(7), objectProps.getSignatureExpire());
        assertEquals(3, objectProps.getRestoreExpireDays());
    }

    @Test
    public void testDefaultValues() {
        // 模拟空配置
        when(mockConfig.getPropertyNames()).thenReturn(new HashSet<>());
        
        // 执行初始化
        cosConfigProperties.initProperties();

        // 验证默认值
        assertEquals("", cosConfigProperties.getAppId());
        assertEquals("", cosConfigProperties.getSecretId());
        assertEquals("", cosConfigProperties.getSecretKey().getPrivateKey());

        // 验证默认客户端配置存在
        assertTrue(cosConfigProperties.getClients().containsKey("default"));
        assertTrue(cosConfigProperties.getObjects().containsKey("default"));
    }
}
