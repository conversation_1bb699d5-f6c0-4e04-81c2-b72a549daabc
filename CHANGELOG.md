# Changelog

## 2.23.0 (2020-08-26)

* [improve] [VOD-1305](http://pm.igeeker.org/browse/VOD-1305) 切换使用云上 Redis
* [improve] [VOD-1269](http://pm.igeeker.org/browse/VOD-1269) 停用OSS内外网连接模式的自动切换

## 2.20.1 (2020-03-21)

* [improve] [LIVE-23288](http://pm.igeeker.org/browse/LIVE-23288) 直播数据库上云

## 2.20.0 (2020-03-12)

* [improve] [LIVE-23799](http://pm.igeeker.org/browse/LIVE-23799) 用Redis的scan代替keys命令

## 2.19.10 (2020-03-03)

* [bugfix] [LIVE-23414](http://pm.igeeker.org/browse/LIVE-23414) 迁移视频接口，兼容源文件在oss上的情况

## 2.19.9 (2020-02-19)

* [improve] [LIVE-22675](http://pm.igeeker.org/browse/LIVE-22675) 为客户赣教云输出非加密的HLS切片文件 - 还原 pc h5 的链接为MP4

## 2.19.8 (2020-02-17)

* [improve] [LIVE-22718](http://pm.igeeker.org/browse/LIVE-22718) 获取存储类型时，如果出错，则默认为OSS
* [improve] [LIVE-22719](http://pm.igeeker.org/browse/LIVE-22719) 视频转加密后，自动进行重编

## 2.19.4 (2020-02-04)

* [improve] [LIVE-22365](http://pm.igeeker.org/browse/LIVE-22365) 优化从 Redis 中读取 videojson 时的机制

## 2.19.3 (2020-02-03)

* [improve] [LIVE-22254](http://pm.igeeker.org/browse/LIVE-22254) 改进 Redis 连接参数
