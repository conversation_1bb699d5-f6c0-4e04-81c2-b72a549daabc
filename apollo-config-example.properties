# ??????? COS ????
# ?????????? Apollo ?????? COS ????
# ????????????? Apollo ? backend.object-storage ?????

# ????
object.storage.tx.appId=your-app-id
object.storage.tx.secretId=your-secret-id
object.storage.tx.secretKey.privateKey=your-secret-key

# ???????
object.storage.tx.clients.default.name=your-bucket-name
object.storage.tx.clients.default.region=ap-guangzhou
object.storage.tx.clients.default.domain=your-custom-domain.com
object.storage.tx.clients.default.endpoint=cos.ap-guangzhou.myqcloud.com
object.storage.tx.clients.default.httpProtocol=https
object.storage.tx.clients.default.signExpired=3600
object.storage.tx.clients.default.connectionRequestTimeout=-1
object.storage.tx.clients.default.connectionTimeout=30000
object.storage.tx.clients.default.socketTimeout=30000
object.storage.tx.clients.default.maxConnectionsCount=1024
object.storage.tx.clients.default.idleConnectionAlive=60000
object.storage.tx.clients.default.readLimit=262145
object.storage.tx.clients.default.userAgent=polyv-cos-client

# ???????????
object.storage.tx.clients.backup.name=your-backup-bucket-name
object.storage.tx.clients.backup.region=ap-beijing
object.storage.tx.clients.backup.domain=your-backup-domain.com
object.storage.tx.clients.backup.endpoint=cos.ap-beijing.myqcloud.com
object.storage.tx.clients.backup.httpProtocol=https

# ??????
object.storage.tx.objects.default.signatureExpireDays=1
object.storage.tx.objects.default.restoreExpireDays=1

# ??????????
object.storage.tx.objects.backup.signatureExpireDays=7
object.storage.tx.objects.backup.restoreExpireDays=3
