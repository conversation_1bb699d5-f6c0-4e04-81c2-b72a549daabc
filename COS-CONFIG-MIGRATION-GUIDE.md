# COS 配置属性迁移指南

## 问题背景

原来的 `COSConfigProperties` 类使用了 `@ConfigurationProperties` 注解，这是 Spring Boot 的特性，在 Spring 3.x 环境中不可用。

## 解决方案

### 方案一：使用 Apollo Config + 手动配置注入（已实现）

#### 1. 修改后的类结构

- **COSConfigProperties**: 主配置类，使用 `@Component` 注解，通过构造函数注入 Apollo Config
- **COSConfigHelper**: 辅助类，用于解析 Apollo 配置中的键名
- **Spring XML 配置**: 在 `applicationContext_base.xml` 中配置相关 Bean

#### 2. 配置方式

在 Apollo 配置中心的 `backend.object-storage` 命名空间中添加配置：

```properties
# 全局配置
object.storage.tx.appId=your-app-id
object.storage.tx.secretId=your-secret-id
object.storage.tx.secretKey.privateKey=your-secret-key

# 客户端配置
object.storage.tx.clients.default.name=your-bucket-name
object.storage.tx.clients.default.region=ap-guangzhou
object.storage.tx.clients.default.domain=your-custom-domain.com
# ... 其他配置
```

#### 3. 使用方式

```java
@Autowired
private COSConfigProperties cosConfigProperties;

// 使用配置
COSConfigProperties.ClientProperties clientProps = cosConfigProperties.getClients().get("default");
```

### 方案二：使用 @Value 注解（备选方案）

如果你更喜欢使用 `@Value` 注解的方式，可以这样实现：

```java
@Component
public class COSConfigProperties {
    
    @Value("${object.storage.tx.appId:}")
    private String appId;
    
    @Value("${object.storage.tx.secretId:}")
    private String secretId;
    
    @Value("${object.storage.tx.secretKey.privateKey:}")
    private String secretKey;
    
    // 对于复杂的嵌套配置，仍然需要手动处理
}
```

### 方案三：使用 Properties 文件（传统方案）

如果不想使用 Apollo，可以使用传统的 Properties 文件：

1. 创建 `cos.properties` 文件
2. 使用 `PropertyPlaceholderConfigurer` 加载配置
3. 通过 `@Value` 注解注入配置值

## 推荐使用方案一

**优点：**
- 保持了原有的配置结构
- 支持动态配置更新（Apollo 特性）
- 类型安全
- 易于维护和扩展

**缺点：**
- 需要手动编写配置解析代码
- 相比 `@ConfigurationProperties` 稍微复杂一些

## 迁移步骤

1. ✅ 移除 `@ConfigurationProperties` 注解
2. ✅ 添加 `@Component` 注解
3. ✅ 添加 Apollo Config 构造函数注入
4. ✅ 实现 `@PostConstruct` 初始化方法
5. ✅ 在 Spring XML 中配置相关 Bean
6. ✅ 在 Apollo 中添加相应配置
7. ✅ 创建配置辅助类 `COSConfigHelper`
8. ✅ 编写单元测试验证配置加载

## 已完成的文件修改

### 1. 主要类文件
- `COSConfigProperties.java` - 主配置类，移除 `@ConfigurationProperties`，添加 Apollo 配置注入
- `COSConfigHelper.java` - 新增配置辅助类，用于解析配置键
- `COSConfigPropertiesTest.java` - 新增单元测试类

### 2. 配置文件
- `applicationContext_base.xml` - 添加腾讯云对象存储相关 Bean 配置
- `apollo-config-example.properties` - Apollo 配置示例文件

### 3. 文档文件
- `COS-CONFIG-MIGRATION-GUIDE.md` - 迁移指南文档

## 使用方式

### 1. 在 Apollo 配置中心添加配置
在 `backend.object-storage` 命名空间中添加以下配置：

```properties
# 全局配置
object.storage.tx.appId=your-app-id
object.storage.tx.secretId=your-secret-id
object.storage.tx.secretKey.privateKey=your-secret-key

# 默认客户端配置
object.storage.tx.clients.default.name=your-bucket-name
object.storage.tx.clients.default.region=ap-guangzhou
object.storage.tx.clients.default.domain=your-custom-domain.com
# ... 其他配置
```

### 2. 在代码中使用
```java
@Resource
private COSConfigProperties cosConfigProperties;

public void someMethod() {
    // 获取客户端配置
    COSConfigProperties.ClientProperties clientProps =
        cosConfigProperties.getClients().get("default");

    // 获取对象配置
    COSConfigProperties.ObjectProperties objectProps =
        cosConfigProperties.getObjects().get("default");
}
```

## 注意事项

1. **配置键命名**: 保持与原来 `@ConfigurationProperties` 相同的配置键结构
2. **默认值处理**: 确保所有配置都有合理的默认值
3. **类型转换**: 手动处理配置值的类型转换（如 Duration、枚举等）
4. **配置验证**: 可以在 `@PostConstruct` 方法中添加配置验证逻辑
5. **动态更新**: 如果需要支持配置的动态更新，可以实现 Apollo 的配置变更监听器
6. **依赖注入**: `COSClientManager` 等依赖类无需修改，继续使用 `@Resource` 注解注入

## 测试建议

1. ✅ 创建单元测试验证配置加载是否正确
2. 测试不同配置场景（默认配置、多客户端配置等）
3. 验证配置的类型转换是否正确
4. 测试配置缺失时的默认值处理
5. 集成测试验证整个 COS 服务是否正常工作
